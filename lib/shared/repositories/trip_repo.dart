import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/checklist_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/payment_settlement_summery_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/ongoing_trip_page/chat_page/models/chat_messages_model.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';

/// User trip related API methods class
final class TripRepository {
  ///  trip repository constructor
  const TripRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// get requested trip
  Future<ApiResult<TripDataModel>> getRequestedTripList(ApiRequest request) {
    return DioRequest<TripDataModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: TripDataModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// get requested trip
  Future<ApiResult<Map<String, dynamic>>> getTripDetail(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// get accepted trip
  Future<ApiResult<TripDataModel>> getAcceptedTripList(ApiRequest request) {
    return DioRequest<TripDataModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: TripDataModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Api remove req trip
  Future<ApiResult<Map<String, dynamic>>> removeReqTrip(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).delete();
  }

  /// for find trip for exclusive and wish list
  Future<ApiResult<ProviderDataModel>> findTransporter(ApiRequest request) {
    return DioRequest<ProviderDataModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ProviderDataModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// get requested trip
  Future<ApiResult<CheckListData>> getCheckList(ApiRequest request) {
    return DioRequest<CheckListData>(
      dio: dio,
      path: request.path!,
      jsonMapper: CheckListData.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// add notes to accepted trip
  Future<ApiResult<Map<String, dynamic>>> addNotes(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// delete notes to accepted trip
  Future<ApiResult<CheckListData>> deleteNotes(ApiRequest request) {
    return DioRequest<CheckListData>(
      dio: dio,
      path: request.path!,
      jsonMapper: CheckListData.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).delete();
  }

  /// rate provider
  Future<ApiResult<Map<String, dynamic>>> rateProvider(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// approve checklist and add Comments
  Future<ApiResult<ChecklistModel>> checklistComments(ApiRequest request) {
    return DioRequest<ChecklistModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ChecklistModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// cancel sub booking
  Future<ApiResult<Map<String, dynamic>>> cancelSubBooking(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// cancel Booked Car
  Future<ApiResult<Map<String, dynamic>>> cancelBookedCar(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// cancel whole booking
  Future<ApiResult<Map<String, dynamic>>> cancelWholeBooking(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// to pay remaining amount
  Future<ApiResult<Map<String, dynamic>>> getRemainAmountSummary(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// to get settlement amount
  Future<ApiResult<PaymentSettlementSummeryModel>> getSettlementAmount(
    ApiRequest request,
  ) {
    return DioRequest<PaymentSettlementSummeryModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: PaymentSettlementSummeryModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// to pay settlement amount
  Future<ApiResult<Map<String, dynamic>>> paySettlementAmount(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// to create payment settlement checkout
  Future<ApiResult<Map<String, dynamic>>> createPaymentSettlementCheckout(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// to get all old messages
  Future<ApiResult<ChatMessagesModel>> getOldChatMessages(ApiRequest request) {
    return DioRequest<ChatMessagesModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ChatMessagesModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// to read all chat messages
  Future<ApiResult<Map<String, dynamic>>> markAllChatMessagesAsReaded(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }
}
