import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/presentation/models/signup_and_signin_model.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/verify_otp_model.dart';
import 'package:transport_match/presentation/modules/notifications_module/models/notification_model.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';

/// User account related API methods class
/// User account related API methods class
final class AccountRepository {
  /// User account repository constructor
  /// User account repository constructor
  const AccountRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  ///Api call login
  Future<ApiResult<SignUpAndSignInModel>> login(ApiRequest request) {
    return DioRequest<SignUpAndSignInModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: SignUpAndSignInModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call registration
  Future<ApiResult<SignUpAndSignInModel>> signUp(ApiRequest request) {
    return DioRequest<SignUpAndSignInModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: SignUpAndSignInModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call verify otp
  Future<ApiResult<VerifyOtpModel>> verifyOtp(ApiRequest request) {
    return DioRequest<VerifyOtpModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: VerifyOtpModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call resend otp
  Future<ApiResult<VerifyOtpModel>> resendOtp(ApiRequest request) {
    return DioRequest<VerifyOtpModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: VerifyOtpModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call forgot password
  Future<ApiResult<Map<String, dynamic>>> forgotPasswordSendOtp(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call reset password
  Future<ApiResult<Map<String, dynamic>>> restPassword(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call logout
  Future<ApiResult<Map<String, dynamic>>> logout(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  ///Api call delete account
  Future<ApiResult<Map<String, dynamic>>> deleteAccount(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).delete();
  }

  ///Api call refresh token
  Future<ApiResult<Map<String, dynamic>>> tokenRefresh(ApiRequest request) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// To generate Url
  Future<ApiResult<List<dynamic>>> generateUrl(
    ApiRequest request,
  ) {
    return DioRequest<List<dynamic>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// To get notification list
  Future<ApiResult<NotificationData>> getNotification(
    ApiRequest request,
  ) {
    return DioRequest<NotificationData>(
      dio: dio,
      path: request.path!,
      jsonMapper: NotificationData.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// To get unread notification count
  Future<ApiResult<Map<String, dynamic>>> getUnreadNotificationCount(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// To mark notification as read
  Future<ApiResult<Map<String, dynamic>>> notificationMarkRead(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  ///Api call to get provider info
  Future<ApiResult<SignUpAndSignInModel>> updateUserInfo(
    ApiRequest request,
  ) {
    return DioRequest<SignUpAndSignInModel>(
      jsonMapper: SignUpAndSignInModel.fromJson,
      dio: dio,
      path: request.path!,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// To get stripe customer portal url
  Future<ApiResult<Map<String, dynamic>>> stripeCustomerPortalUrl(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// To create complaint
  Future<ApiResult<Map<String, dynamic>>> addComplaint(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// To get customer detail
  Future<ApiResult<UserDetailData>> getCustomerDetail(
    ApiRequest request,
  ) {
    return DioRequest<UserDetailData>(
      dio: dio,
      path: request.path!,
      jsonMapper: UserDetailData.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// To update customer detail
  Future<ApiResult<UserModel>> addUpdateCustomerDetail(
    ApiRequest request,
  ) {
    return DioRequest<UserModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: UserModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

   /// for update language
  Future<ApiResult<Map<String, dynamic>>> updateLanguage(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// To add customer detail
}
