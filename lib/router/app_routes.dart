/// App Routes constants
///
/// Naming convention:
/// - Use camelCase for route names
/// - For screen routes, use the format: moduleNameScreenName
/// - Path should be kebab-case version of the route name
///
/// Example:
/// - Route name: authLoginScreen
/// - Path: /auth/login-screen
/// - Widget: LoginScreen
class AppRoutes {
  // <---------------------------------------------- Initial routes ---------------------------------------------->
  static const String initial = '/';
  static const String initialPath = '/';
  static const String upgradePath = '/upgrade';

  // <---------------------------------------------- Auth Module ---------------------------------------------->
  static const String authBase = '/auth';
  static const String authLoginScreen = 'auth_login';
  static const String authSignupPath = 'signup';
  static const String authSignupScreen = 'auth_signup';
  static const String authResetPasswordPath = 'reset-password';
  static const String authResetPasswordScreen = 'auth_reset_password';
  static const String authForgotPasswordPath = 'forgot-password';
  static const String authForgotPasswordScreen = 'auth_forgot_password';
  static const String authCheckOtpPath = 'check-otp';
  static const String authCheckOtpScreen = 'auth_check_otp';

  // <---------------------------------------------- Common Booking Pages ---------------------------------------------->

  static const String bookingShipmentConfirmationPath =
      '/booking/shipment-confirmation';
  static const String bookingShipmentConfirmationScreen =
      'booking_shipment_confirmation';
  static const String bookingPaymentPath = '/booking/payment';
  static const String bookingPaymentScreen = 'booking_payment';

  // <---------------------------------------------- Home Module ---------------------------------------------->
  static const String homeBase = '/home';
  static const String homeTransporterListPath = '/home/<USER>';
  static const String homeTransporterListScreen = 'home_transporter_list';
  static const String homeEmptyTransporterPath = '/home/<USER>';
  static const String homeEmptyTransporterScreen = 'home_empty_transporter';
  static const String homeUserInfoPath = '/home/<USER>';
  static const String homeUserInfoScreen = 'home_user_info';
  static const String homeAddressSearchPath = '/home/<USER>';
  static const String homeAddressSearchScreen = 'home_address_search';

  // <---------------------------------------------- Trips Module ---------------------------------------------->
  static const String tripsBase = '/trips';
  static const String tripsTripDetailsPath = '/trips/trip-details';
  static const String tripsTripDetailsScreen = 'trips_trip_details';
  static const String tripsChatPath = '/trips/chat';
  static const String tripsChatScreen = 'trips_chat';
  static const String tripsCarPaymentDetailPath = '/trips/car-payment-detail';
  static const String tripsCarPaymentDetailScreen = 'trips_car_payment_detail';
  static const String tripsChecklistPath = '/trips/checklist';
  static const String tripsChecklistScreen = 'trips_checklist';
  static const String tripsRemainPaymentPath = '/trips/remain-payment';
  static const String tripsRemainPaymentScreen = 'trips_remain_payment';
  static const String tripsPaymentSettelementPath = '/trips/payment-settlement';
  static const String tripsPaymentSettelementScreen = 'trips_payment_settlement';
  static const String tripsUserInfoPath = '/trips/user-info';
  static const String tripsUserInfoScreen = 'trips_user_info';
  static const String tripsRequestedTripsPath = '/trips/requested-trips';
  static const String tripsRequestedTripsScreen = 'trips_requested_trips';
  static const String stockLocationsPath = '/trips/stock-locations';
  static const String stockLocationsScreen = 'stock_locations';
  static const String carInfoPath = '/trips/car-info';
  static const String carInfoSheet = 'car_info';
  static const String tripsAuctionPath = '/trips/auction';
  static const String tripsAuctionScreen = 'trips_auction';
  static const String tripsAddNotePath = '/trips/add-note';
  static const String tripsAddNoteScreen = 'trips_add_note';
  static const String tripsEditRestedTripPath = '/trips/edit-rested-trip';
  static const String tripsEditRestedTripScreen = 'trips_edit_rested_trip';
  static const String tripsRatePath = '/trips/rate';
  static const String tripsRateScreen = 'trips_rate';
  static const String tripsRestedRequestedTripsPath =
      '/trips/rested-requested-trips';
  static const String tripsRestedRequestedTripsScreen =
      'trips_rested_requested_trips';
  static const String tripsTripTransporterListPath =
      '/trips/trip-transporter-list';
  static const String tripsTripTransporterListScreen =
      'trips_trip_transporter_list';
  static const String tripsShowChecklistPath = '/trips/show-checklist';
  static const String tripsShowChecklistScreen = 'tripsShowChecklistScreen';
  static const String tripsTripShipmentConfirmationPath =
      '/trips/trip-shipment-confirmation';
  static const String tripsTripShipmentConfirmationScreen =
      'tripShipmentConfirmationScreen';

  // <---------------------------------------------- Notification Module ---------------------------------------------->
  // Base path
  static const String notificationBase = '/notification';

  // <---------------------------------------------- Profile Module ---------------------------------------------->
  static const String profileBase = '/profile';
  static const String profileEditPath = '/profile/edit';
  static const String profileEditScreen = 'profile_edit';
  static const String profileSetNewPasswordPath = '/profile/set-new-password';
  static const String profileSetNewPasswordScreen = 'profile_set_new_password';
  static const String profileCustomerSupportPath = '/profile/customer-support';
  static const String profileCustomerSupportScreen = 'profile_customer_support';
  static const String profileCustomerDetailPath = '/profile/customer-detail';
  static const String profileCustomerDetailScreen = 'profile_customer_detail';
  // <---------------------------------------------- Common Module ---------------------------------------------->
  static const String commonBase = '/common';
  static const String commonWebViewPath = 'webview';
  static const String commonWebViewScreen = 'common_webview';
}
