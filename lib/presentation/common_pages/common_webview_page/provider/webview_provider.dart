// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewProvider extends ChangeNotifier {
  WebViewProvider(CommonWebViewParams webviewData) {
    
    '/// noww webviewData : ${webviewData.bookingId}'.logV;
    if (webviewData.url.isNotEmptyAndNotNull) {
      webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              '/// noww urlll : $url'.logV;
              isWebViewShowLoader.value = true;
              if (url.contains('providers') || url.contains('tagline')) {
                AppNavigationService.pop(webviewData.context);
              }
            },
            onPageFinished: (String url) {
              '/// noww url finish : $url'.logV;
              '/// noww url payments : ${url.contains('https://staging.d171pkw491jul9.amplifyapp.com/signin/')}'.logV;
              if(url.contains('https://staging.d171pkw491jul9.amplifyapp.com/signin/')) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Future.delayed(const Duration(milliseconds: 100), () {
                    // AppNavigationService.pushAndRemoveAllPreviousRoute(
                    //   context,
                    //   AppRoutes.authBase,
                    //   isBaseRoute: true,
                    // );
                    AppNavigationService.pushAndRemoveAllPreviousRoute(
                        webviewData.context,
                        AppRoutes.homeBase,
                    );
                  });
                });
              }
              isWebViewShowLoader.value = false;
            },
          ),
        )
        ..loadRequest(Uri.parse(webviewData.url ?? ''));
    } else {
      '/// noww webviewData : ${webviewData.bodyData}'.logV;
      '/// noww webviewData bookingId : ${webviewData.bookingId}'.logV;
      getBookingUrl(
        webviewData.context,
        bodyData: webviewData.bodyData,
        isExclusiveTrip: webviewData.isExclusiveTrip,
        isFromHome: webviewData.isFromHome,
        bookingId: webviewData.bookingId,
        isRemainAmountAPI: webviewData.isRemainAmountAPI,
      );
    }
  }

  final isWebViewShowLoader = ValueNotifier<bool>(false);
  String url = '';
  bool _isClosed = false;

  WebViewController? webViewController;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'acceptedTripProvider notify error: $e'.logE;
    }
  }

  /// get payment information
  CancelToken? getBookingCancelToken;
  final isShowLoader = ValueNotifier<bool>(false);
  Future<void> getBookingUrl(
    BuildContext context, {
    bool isExclusiveTrip = false,
    bool isFromHome = false,
    bool isRemainAmountAPI = false,
    String? bookingId,
    required Map<String, dynamic>? bodyData,
  }) async {
    getBookingCancelToken?.cancel();
    getBookingCancelToken = CancelToken();
    final homeRepository = Injector.instance<HomeRepository>();

    try {
      '/// noww bodyData : ${bodyData.toString()}'.logV;
      isShowLoader.value = true;
      final response = await homeRepository.getBookingUrl(
        ApiRequest(
          path: isRemainAmountAPI
              ? isExclusiveTrip
                    ? EndPoints.getRemainExclusiveBookingUrl
                    : EndPoints.getRemainBookingUrl
              : isExclusiveTrip
              ? EndPoints.getExclusiveBookingUrl
              : EndPoints.getBookingUrl,
          cancelToken: getBookingCancelToken,
          data: bodyData,
        ),
      );
      isShowLoader.value = false;
      response.when(
        success: (data) {
          '/// rrr bookingId : ${data}'.logV;
          '/// noww data[AppStrings.bookingId] : ${data[AppStrings.bookingId]}'.logV;
          if (_isClosed || (getBookingCancelToken?.isCancelled ?? true)) return;
          context.l10n.pleaseWaitWeAreRedirectingPayment.showSuccessAlert();
          webViewController = WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..setNavigationDelegate(
              NavigationDelegate(
                onProgress: (int progress) {
                  // Update loading bar.
                },
                onPageStarted: (String url) {
                  '/// noww bookingId // pageStarted= $bookingId'.logD;
                  '/// noww onPageStarttttt = $url'.logV;
                  '/// isFromHome = $isFromHome'.logV;
                  if (isFromHome) {
                    // if (url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/successful')) {
                    //   '//// navigate through here 1'.logV;
                    //   '//// navigate through here 1 bookingId = ${data[AppStrings.bookingId]}'.logV;
                    //   AppNavigationService.pushAndRemoveAllPreviousRoute(
                    //     context,
                    //     AppRoutes.homeUserInfoScreen,
                    //     extra: (data[AppStrings.bookingId])
                    //         ?.toString(),
                    //   );
                    //   return;
                    // } else {
                      isWebViewShowLoader.value = true;
                    // }
                  }
                 else if (url.contains('providers/') || url.contains('tagline')) {
                    if (isRemainAmountAPI) {
                      '//// navigate through here 2'.logV;
                      AppNavigationService.pushAndRemoveAllPreviousRoute(
                        context,
                        AppRoutes.homeUserInfoScreen,
                      );
                      return;
                    }
                    AppNavigationService.pop(context);
                    return;
                  } else {
                    isWebViewShowLoader.value = true;
                  }
                },
                onPageFinished: (String url) {
              '/// noww bookingId // pageFinish= $bookingId'.logD;  
              '/// noww bookingId ?? = ${data[AppStrings.bookingId]}'.logD;
              '/// noww webviewdata.bookingId '
              '/// noww my url = $url'.logD;
              '/// noww isContain success = ${url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/successful')}'.logD;
              '/// noww isContain failed = ${url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/failed/')}'.logD;
              '/// noww isRemainAmountAPI = $isRemainAmountAPI'.logD;
              if(url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/successful') && isRemainAmountAPI) {
                '/// navigate hereee home'.logV;
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Future.delayed(const Duration(seconds: 5), () {
                    // AppNavigationService.pushAndRemoveAllPreviousRoute(
                    //   context,
                    //   AppRoutes.authBase,
                    //   isBaseRoute: true,
                    // );
                    AppNavigationService.pushAndRemoveAllPreviousRoute(
                        context,
                        AppRoutes.homeBase,
                    );
                  });
                });
              }
              else if(url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/successful') && !isRemainAmountAPI) {
                '/// navigate hereee userInfo'.logV;
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  '//// navigate through here 3'.logV;
                  '//// navigate through here 3 bookingId = ${data[AppStrings.bookingId]}'.logV;
                  Future.delayed(const Duration(seconds: 5), () {
                    '/// after bookingId = $bookingId'.logD;
                    '/// after bookingId ?? = ${data[AppStrings.bookingId]}'.logD;
                    AppNavigationService.pushAndRemoveAllPreviousRoute(
                        context,
                        AppRoutes.homeUserInfoScreen,
                        extra: (data[AppStrings.bookingId])
                            ?.toString(),
                    );
                  });
                });
              }
              else if(url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/failed/')) {
                '/// navigate hereee back failed'.logV;
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Future.delayed(const Duration(seconds: 5), () {
                    // AppNavigationService.pushAndRemoveAllPreviousRoute(
                    //   context,
                    //   AppRoutes.authBase,
                    //   isBaseRoute: true,
                    // );
                    AppNavigationService.pop(context);
                  });
                });
              }

                  isWebViewShowLoader.value = false;
                },
                onHttpError: (HttpResponseError error) {},
                onWebResourceError: (WebResourceError error) {},
              ),
            )
            ..loadRequest(Uri.parse(data['session_url']?.toString() ?? ''));
          // AppNavigationService.pushNamed(
          //   context,
          //   AppRoutes.commonWebViewScreen,
          //   extra: CommonWebViewParams(
          //     url: data['session_url'].toString(),
          //     isExclusiveTrip: isExclusiveTrip,
          //   ),

          //   //  CommonWebView(
          //   //   url: data['session_url'].toString(),
          //   //   isExclusiveTrip: isExclusiveTrip,
          //   // ),
          // );
          notify();
        },
        error: (error) {
          if (_isClosed || (getBookingCancelToken?.isCancelled ?? true)) return;
          if (error.code == 308) {
            AppNavigationService.pushAndRemoveAllPreviousRoute(
              context,
              AppRoutes.homeBase,
              isBaseRoute: true,
            );
          }
          AppNavigationService.pop(context);
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getBookingCancelToken?.isCancelled ?? true)) return;
      AppNavigationService.pop(context);
      context.l10n.somethingWentWrong.showErrorAlert();
      'getBookingUrl Error: $e'.logE;
    }
  }

  @override
  void dispose() {
    getBookingCancelToken?.cancel();
    _isClosed = true;

    super.dispose();
  }
}
