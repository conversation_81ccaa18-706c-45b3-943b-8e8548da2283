import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

/// Payment summary row
class PaymentSummaryRow extends StatelessWidget {
  /// Constructor
  const PaymentSummaryRow({
    required this.title,
    required this.amount,
    this.isLast = false,
    super.key,
  });

  /// Title
  final String title;

  /// Amount
  final String amount;
  
  /// IsLast
  final bool? isLast;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: AppSize.h8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: AppSize.w10,
        children: [
          Flexible(
            child: Text(
              title,
              style: (isLast ?? false) ? context.textTheme.bodyLarge?.copyWith(
                      color: AppColors.ff212529,
                    ): context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: AppSize.sp14,
                color: AppColors.black,
              ),
            ),
          ),
          Text(
            amount,
            maxLines: 1,
            style: (isLast ?? false) ? context.textTheme.bodyLarge?.copyWith(
                      color: AppColors.ff212529,
                    ): context.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w400,
              color: AppColors.ff495057,
            ),
          ),
        ],
      ),
    );
  }
}

/// Payment total section
class TotalRow extends StatelessWidget {
  /// Constructor
  const TotalRow({
    super.key,
    required this.firstTitle,
    required this.lastTitle,
    this.padding,
    this.isLast = false,
  });

  /// Payment FirstTitle
  final String firstTitle;

  /// Payment FirstTitle
  final String lastTitle;

  /// Payment LastTitle FontWeight
  final double? padding;

  final bool isLast;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.h16,
        vertical: padding ?? AppSize.h8,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r4),
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: context.width * 0.5,
            child: Text(
              firstTitle,
              style: isLast
                  ? context.textTheme.titleLarge?.copyWith(
                      color: AppColors.ff212529,
                    )
                  : context.textTheme.titleLarge?.copyWith(
                      fontSize: AppSize.sp18,
                      color: AppColors.ff212529,
                      fontWeight: FontWeight.w500,
                    ),
            ),
          ),
          Flexible(
            child: MarqueeWidget(
              child: Text(
                lastTitle,
                maxLines: 1,
                style: isLast
                    ? context.textTheme.titleLarge
                    : context.textTheme.titleLarge?.copyWith(
                        fontSize: AppSize.sp18,
                        color: AppColors.black,
                        fontWeight: FontWeight.w500,
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}


class TotalRowWithDetails extends StatelessWidget {
  /// Constructor
  const TotalRowWithDetails({
    super.key,
    required this.firstTitle,
    required this.lastTitle,
    required this.tax,
    this.padding,
    this.isLast = false,
  });

  /// Payment FirstTitle
  final String firstTitle;

  /// Payment FirstTitle
  final String lastTitle;

  /// Payment LastTitle FontWeight
  final double? padding;

  /// Payment LastTitle FontWeight
  final String tax;

  final bool isLast;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.h16,
        vertical: padding ?? AppSize.h8,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: context.width * 0.5,
                child: Text(
                  firstTitle,
                  style: isLast
                      ? context.textTheme.titleLarge?.copyWith(
                          color: AppColors.ff212529,
                        )
                      : context.textTheme.titleLarge?.copyWith(
                          fontSize: AppSize.sp18,
                          color: AppColors.ff212529,
                          fontWeight: FontWeight.w500,
                        ),
                ),
              ),
              Flexible(
                child: MarqueeWidget(
                  child: Text(
                    lastTitle,
                    maxLines: 1,
                    style: isLast
                        ? context.textTheme.titleLarge
                        : context.textTheme.titleLarge?.copyWith(
                            fontSize: AppSize.sp18,
                            color: AppColors.black,
                            fontWeight: FontWeight.w500,
                          ),
                  ),
                ),
              ),
            ],
          ),
          _buildSummaryRow(context, '• ${context.l10n.tax}', '${tax}',),
        ],
      ),
    );
  }
}

 /// Build summary row with optional net amount
  Widget _buildSummaryRow(
    BuildContext context,
    String title,
    String amount, {
    String? netAmount,
  }) {
    return Padding(
      padding: EdgeInsets.only(top: AppSize.h8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: AppSize.sp14,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primaryColor,
                ),
              ),
              Text(
                amount,
                style: TextStyle(
                  fontSize: AppSize.sp14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
          if (netAmount != null && netAmount.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: AppSize.h4, left: AppSize.w16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '• ${context.l10n.netYouPay}',
                    style: TextStyle(
                      fontSize: AppSize.sp12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  Text(
                    netAmount,
                    style: TextStyle(
                      fontSize: AppSize.sp12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }