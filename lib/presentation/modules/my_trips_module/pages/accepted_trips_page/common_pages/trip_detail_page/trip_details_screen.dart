import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:readmore/readmore.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/models/trip_details_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/provider/trip_detail_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/widgets/details_card_widgets.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/widgets/location_widget.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/widgets/notes_widget.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/widgets/origin_drop_location_widget.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/widgets/trip_payment_summary.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/widgets/trip_transporter_card.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// details page ui
class TripDetailsScreen extends StatelessWidget {
  /// Constructor
  const TripDetailsScreen({super.key, required this.tripDetailsParams});

  final TripDetailsParams tripDetailsParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) =>
          TripDetailProvider()..getTripDetails(tripDetailsParams.id, context),
      builder: (context, child) {
        return Selector<
          TripDetailProvider,
          (TripModel?, bool, bool, bool, bool)
        >(
          selector: (context, tripDetailProvider) => (
            tripDetailProvider.selectedTrip,
            tripDetailProvider.isDeleteNotesLoad,
            tripDetailProvider.isPayRemainingAmountLoad,
            tripDetailProvider.isShowTripDetailLoader,
            tripDetailProvider.isReqDataShowLoader,
          ),
          builder: (context, data, _) {
            final tripDetailProvider = context.read<TripDetailProvider>();
            final isCompleted =
                data.$1?.bookingStatus == BookingStatusType.COMPLETED.name;
            '||||||| booking detail length = ${data.$1?.bookingDetails?.length}'
                .logV;
            return Scaffold(
              backgroundColor: AppColors.ffF8F9FA,
              appBar: CustomAppBar(title: context.l10n.tripDetails),
              body: AppLoader(
                isShowLoader: data.$2 || data.$3 || data.$4 || data.$5,
                child: data.$4
                    ? SizedBox(
                        height: context.height * 0.8,
                        width: context.width,
                      )
                    : EasyRefresh(
                        triggerAxis: Axis.vertical,
                        header: AppCommonFunctions.getLoadingHeader(),
                        footer: AppCommonFunctions.getLoadingFooter(),
                        controller: tripDetailProvider.refreshController,
                        onRefresh: () async => tripDetailProvider
                            .getTripDetails(tripDetailsParams.id, context),
                        child: data.$1 == null && !data.$4
                            ? ListView(
                                children: [
                                  SizedBox(
                                    height:
                                        MediaQuery.of(context).size.height *
                                        0.8,
                                    child: Center(
                                      child: Text(context.l10n.noTripFound),
                                    ),
                                  ),
                                ],
                              )
                            : SingleChildScrollView(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSize.appPadding,
                                ),
                                child: Column(
                                  children: [
                                    TripTransporterCard(data: data.$1!),
                                    TripLocationWidget(
                                      title: context.l10n.originLocation,
                                      value:
                                          data
                                              .$1
                                              ?.startStopLocation
                                              ?.fullAddress ??
                                          data.$1?.userStartLocation?.street ??
                                          '',
                                      // name: data.$1?.startStopLocation?.name,
                                      // receiverId:
                                      //     data.$1?.startStopLocation?.adminUserId,
                                      // bookingDetailId:
                                      //     data.$1?.startStopLocation?.id,
                                      // chatType: ChatType.originStock,
                                    ),
                                    TripLocationWidget(
                                      title: context.l10n.dropLocation,
                                      value:
                                          data
                                              .$1
                                              ?.endStopLocation
                                              ?.fullAddress ??
                                          data.$1?.userEndLocation?.street ??
                                          '',
                                      // name: data.$1?.endStopLocation?.name,
                                      // receiverId:
                                      //     data.$1?.endStopLocation?.adminUserId,
                                      // bookingDetailId:
                                      //     data.$1?.endStopLocation?.id,
                                      // chatType: ChatType.dropStock,
                                      // customerChatRoomParameter: data.$1
                                      //     ?.endStopLocation?.driverChatRoomDetail,
                                    ),

                                    /// user added notes widget
                                    NotesWidget(
                                      onBack: () =>
                                          tripDetailsParams.onBack?.call(),
                                    ),

                                    /// origin and drop location widget
                                    if (data.$1?.startStopDetailLocation !=
                                            null ||
                                        data.$1?.endStopDetailLocation != null)
                                      Padding(
                                        padding: EdgeInsets.only(
                                          bottom: AppSize.h16,
                                        ),
                                        child: OriginDropLocationWidget(
                                          data: data.$1,
                                        ),
                                      ),

                                    /// booking provider detail widget
                                    if (data.$1?.bookingDetails?.isNotEmpty ??
                                        false)
                                      ListView.builder(
                                        shrinkWrap: true,
                                        itemCount:
                                            data.$1?.bookingDetails?.length ??
                                            0,
                                        padding: EdgeInsets.zero,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemBuilder: (context, index) {
                                          final booking =
                                              data.$1?.bookingDetails?[index];
                                          return Padding(
                                            padding: EdgeInsets.only(
                                              bottom: AppSize.h16,
                                            ),
                                            child: DetailsCardWidgets(
                                              data: booking,
                                              //:TODO :  NEED TO RECONFIRM
                                              index: index,
                                              tripData: data.$1,
                                              isExclusive:
                                                  data.$1?.bookingType ==
                                                  BookingType.EXCLUSIVE.name,
                                              onRatingDone:
                                                  (
                                                    rating,
                                                    description,
                                                  ) => tripDetailProvider
                                                    ..selectedTrip
                                                        ?.bookingDetails?[index]
                                                        .ratings = [
                                                      RatingModel(
                                                        rating: rating,
                                                        suggestion: description,
                                                      ),
                                                    ]
                                                    ..notify(),
                                              addDriverChatRoomData:
                                                  (
                                                    chatRoomId,
                                                    isActive,
                                                    chatIndex,
                                                    isCount,
                                                  ) {
                                                    context
                                                        .read<
                                                          TripDetailProvider
                                                        >()
                                                        .clearChatCountNAssignModel(
                                                          context,
                                                          chatIndex: chatIndex,
                                                          index: index,
                                                          chatRoomId:
                                                              chatRoomId,
                                                          isActive: isActive,
                                                          isCount: isCount,
                                                        );
                                                  },
                                            ),
                                          );
                                        },
                                      ),
                                    if (!isCompleted)
                                      Selector<
                                        TripDetailProvider,
                                        List<AssigneeModel>?
                                      >(
                                        selector: (p0, p1) =>
                                            p1.selectedTrip?.bookingAssignee,
                                        builder: (context, bookingAssignee, _) {
                                          return Padding(
                                            padding: EdgeInsets.only(
                                              bottom: AppSize.h16,
                                            ),
                                            child: GestureDetector(
                                              onTap: () => tripDetailProvider
                                                  .navigateToUserInfoScreen(
                                                    context,
                                                    (assigneeModel) =>
                                                        tripDetailProvider
                                                                .selectedTrip
                                                                ?.bookingAssignee =
                                                            assigneeModel ?? [],
                                                  ),
                                              child: DecoratedBox(
                                                decoration: BoxDecoration(
                                                  color: AppColors.white,
                                                  borderRadius:
                                                      BorderRadius.all(
                                                        Radius.circular(
                                                          AppSize.r4,
                                                        ),
                                                      ),
                                                ),
                                                child: Align(
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child: Padding(
                                                    padding: EdgeInsets.all(
                                                      AppSize.r16,
                                                    ),
                                                    child: Text(
                                                      (bookingAssignee
                                                                  ?.isNotEmpty ??
                                                              false)
                                                          ? context
                                                                .l10n
                                                                .dropAndPickupPerson
                                                          : context
                                                                .l10n
                                                                .addDropAndPickupPerson,
                                                      style: context
                                                          .textTheme
                                                          .bodyLarge,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),

                                    if (data.$1?.bookingDetails?.any(
                                          (element) =>
                                              element
                                                  .tripData
                                                  ?.reportList
                                                  ?.isNotEmpty ??
                                              false,
                                        ) ??
                                        false)
                                      Align(
                                        alignment: Alignment.centerLeft,
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                            bottom: AppSize.h5,
                                          ),
                                          child: Text(
                                            context.l10n.reportsFromTransporter,
                                            style: context.textTheme.bodyLarge
                                                ?.copyWith(
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ),
                                      ),
                                    if (data.$1?.bookingDetails?.isNotEmpty ??
                                        false)
                                      ListView.builder(
                                        shrinkWrap: true,
                                        itemCount:
                                            data.$1?.bookingDetails?.length ??
                                            0,
                                        padding: EdgeInsets.zero,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemBuilder: (context, index) {
                                          final booking =
                                              data.$1?.bookingDetails?[index];
                                          return (booking
                                                      ?.tripData
                                                      ?.reportList
                                                      ?.isEmpty ??
                                                  true)
                                              ? const SizedBox()
                                              : Container(
                                                  decoration: BoxDecoration(
                                                    color: AppColors.white,
                                                    borderRadius:
                                                        BorderRadius.all(
                                                          Radius.circular(
                                                            AppSize.r4,
                                                          ),
                                                        ),
                                                  ),
                                                  padding: EdgeInsets.all(
                                                    AppSize.sp16,
                                                  ),
                                                  margin: EdgeInsets.only(
                                                    bottom: AppSize.h16,
                                                  ),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        booking
                                                                ?.tripData
                                                                ?.companyName ??
                                                            '',
                                                        style: context
                                                            .textTheme
                                                            .bodyMedium
                                                            ?.copyWith(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              fontSize:
                                                                  AppSize.sp16,
                                                              color: AppColors
                                                                  .black,
                                                            ),
                                                      ),
                                                      ListView.separated(
                                                        separatorBuilder:
                                                            (
                                                              context,
                                                              index,
                                                            ) => Padding(
                                                              padding:
                                                                  EdgeInsets.symmetric(
                                                                    horizontal:
                                                                        AppSize
                                                                            .h20,
                                                                    vertical:
                                                                        AppSize
                                                                            .h2,
                                                                  ),
                                                              child: const Divider(
                                                                color: AppColors
                                                                    .ffE6E6E6,
                                                              ),
                                                            ),
                                                        shrinkWrap: true,
                                                        itemCount:
                                                            booking
                                                                ?.tripData
                                                                ?.reportList
                                                                ?.length ??
                                                            0,
                                                        padding:
                                                            EdgeInsets.only(
                                                              top: AppSize.h5,
                                                            ),
                                                        physics:
                                                            const NeverScrollableScrollPhysics(),
                                                        itemBuilder: (context, index) {
                                                          final report = booking
                                                              ?.tripData
                                                              ?.reportList?[index];

                                                          final listLength =
                                                              booking
                                                                  ?.tripData
                                                                  ?.reportList
                                                                  ?.length ??
                                                              0;
                                                          return Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            spacing: AppSize.h2,
                                                            children: [
                                                              Row(
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  Text(
                                                                    listLength ==
                                                                            1
                                                                        ? AppStrings
                                                                              .arrow
                                                                        : '   ${index + 1}.  ',
                                                                    style: context
                                                                        .textTheme
                                                                        .titleMedium
                                                                        ?.copyWith(
                                                                          fontSize:
                                                                              AppSize.sp13,
                                                                          color:
                                                                              AppColors.black,
                                                                        ),
                                                                  ),
                                                                  Flexible(
                                                                    child: ReadMoreText(
                                                                      report?.description ??
                                                                          '',
                                                                      style: context
                                                                          .textTheme
                                                                          .titleMedium
                                                                          ?.copyWith(
                                                                            fontSize:
                                                                                AppSize.sp13,
                                                                            color:
                                                                                AppColors.black,
                                                                          ),
                                                                      trimMode:
                                                                          TrimMode
                                                                              .Line,
                                                                      trimCollapsedText: context
                                                                          .l10n
                                                                          .readMore,
                                                                      trimExpandedText:
                                                                          ' ${context.l10n.readLess}',
                                                                      lessStyle: context
                                                                          .textTheme
                                                                          .titleMedium
                                                                          ?.copyWith(
                                                                            fontSize:
                                                                                AppSize.sp12,
                                                                            color:
                                                                                AppColors.ff0087C7,
                                                                          ),
                                                                      moreStyle: context
                                                                          .textTheme
                                                                          .titleMedium
                                                                          ?.copyWith(
                                                                            fontSize:
                                                                                AppSize.sp12,
                                                                            color:
                                                                                AppColors.ff0087C7,
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              Align(
                                                                alignment: Alignment
                                                                    .centerRight,
                                                                child: Text(
                                                                  '${context.l10n.affectedTime}: '
                                                                  '${tripDetailProvider.formateAffectedTime(report?.affectedTime, context)}'
                                                                  ' ${context.l10n.hour}',
                                                                  style: context
                                                                      .textTheme
                                                                      .titleMedium
                                                                      ?.copyWith(
                                                                        fontSize:
                                                                            AppSize.sp12,
                                                                        color: AppColors
                                                                            .ff495057,
                                                                      ),
                                                                ),
                                                              ),
                                                            ],
                                                          );
                                                        },
                                                      ),
                                                    ],
                                                  ),
                                                );
                                        },
                                      ),

                                    /// trip payment summary widget
                                    const TripPaymentSummary(),
                                    Gap(AppSize.h16),
                                  ],
                                ),
                              ),
                      ),
              ),
              bottomNavigationBar: data.$4 || isCompleted || data.$1 == null
                  ? null
                  : AppButton(
                      text: context.l10n.cancelBooking,
                      isFillButton: false,
                      borderColor: AppColors.errorColor,
                      onPressed: () async {
                        await context.showCancelDialogWithReasons(
                          defaultActionText: context.l10n.yes,
                          cancelActionText: context.l10n.no,
                          controller: tripDetailProvider
                              .cancelBookingOrTripOrCarReasonController,
                          dropDownHintText: context.l10n.reason,
                          onCancelActionPressed: Navigator.pop,
                          onDefaultActionPressed: (context) async {
                            if (tripDetailProvider
                                    .cancelBookingOrTripOrCarReasonController
                                    .dropDownValue ==
                                null) {
                              context.l10n.pleaseSelectReason.showErrorAlert();
                              return;
                            }
                            AppNavigationService.pop(context);
                            await tripDetailProvider.removeWholeBooking(
                              tripDetailsParams.id,
                              context,
                              tripDetailProvider
                                      .cancelBookingOrTripOrCarReasonController
                                      .dropDownValue
                                      ?.value
                                  as String,
                            );
                          },
                          titleWidget: Text(
                            context.l10n.cancelBooking,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: AppSize.sp22,
                              fontWeight: FontWeight.w500,
                              color: AppColors.ff6C757D,
                            ),
                          ),
                          content: context.l10n.areUSureCancelBooking,
                          reasons: [
                            {
                              'key': 'CHANGE_OF_PLANS',
                              'label': context.l10n.changeInTravelPlans,
                            },
                            {
                              'key': 'BOOKED_BY_MISTAKE',
                              'label': context.l10n.bookingMadeAccidentally,
                            },
                            {
                              'key': 'FOUND_ALTERNATIVE',
                              'label': context.l10n.foundBetterAlternative,
                            },
                            {
                              'key': 'TOO_EXPENSIVE',
                              'label': context.l10n.costTooHigh,
                            },
                            {
                              'key': 'PICKUP_ISSUE',
                              'label': context.l10n.pickupIssue,
                            },
                            {
                              'key': 'NEED_TO_EDIT_BOOKING',
                              'label': context.l10n.needToEditBooking,
                            },
                            {
                              'key': 'OTHER',
                              'label': context.l10n.anyOtherReason,
                            },
                          ],
                        );
                      },
                      textStyle: context.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.errorColor,
                        fontSize: AppSize.sp16,
                      ),
                    ),
            );
          },
        );
      },
    );
  }
}
