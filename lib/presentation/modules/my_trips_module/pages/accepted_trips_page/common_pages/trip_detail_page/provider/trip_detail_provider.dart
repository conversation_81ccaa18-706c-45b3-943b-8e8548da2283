import 'dart:async';

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/payment_settlement_page/models/payment_settlement_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/remain_payment_summary_page/models/remain_payment_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/trip_user_info_page/models/trip_user_info_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/payment_summary_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class TripDetailProvider extends ChangeNotifier {
  EasyRefreshController refreshController = EasyRefreshController();

  /// Flag to check if provider is closed
  final bool _isClosed = false;
  final tripRepo = Injector.instance<TripRepository>();
  TripModel? selectedTrip;

  /// Payment summary data
  ValueNotifier<PaymentSummaryData?> paymentData = ValueNotifier(null);

  final SingleValueDropDownController cancelBookingOrTripOrCarReasonController =
      SingleValueDropDownController();

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'acceptedTripProvider notify error: $e'.logE;
    }
  }

  /// Get trip details
  /// [id] is the trip ID
  /// [context] is the BuildContext
  /// [isCompleted] flag to indicate if trip is completed
  CancelToken? getTripDetailsToken;
  bool isShowTripDetailLoader = false;
  Future<dynamic> getTripDetails(
    String id,
    BuildContext context, {
    bool isCompleted = false,
  }) async {
    if (_isClosed) return;

    getTripDetailsToken?.cancel();
    getTripDetailsToken = CancelToken();
    // unawaited(
    //   AppNavigationService.pushNamed<AcceptedTripProvider>(
    //     context,
    //     TripDetailsScreen(isCompleted: isCompleted),
    //     controller: context.read<AcceptedTripProvider>(),
    //   ),
    // );
    // try {
    final url = '${EndPoints.acceptedTrip}/$id/';
    isShowTripDetailLoader = true;
    notify();
    try {
      final response = await tripRepo.getTripDetail(
        ApiRequest(path: url, cancelToken: getTripDetailsToken),
      );
      isShowTripDetailLoader = false;
      if (_isClosed) return;

    response.when(
      success: (data) {
        if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
        selectedTrip = TripModel.fromJson(data);
        data.logE;
        final isExclusive =
            selectedTrip?.bookingType == BookingType.EXCLUSIVE.name;
        getRemainingPaymentSummary(id, context, isExclusive: isExclusive);
      },
      error: (error) {
        if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
        error.message.showErrorAlert();
      },
    );
    notify();
     } catch (e) {
      if (_isClosed || (getTripDetailsToken?.isCancelled ?? true)) return;
       isShowTripDetailLoader = false;
       notify();
     'getTripDetails error: $e'.logE;
      if (kDebugMode) e.toString().showErrorAlert();
     }
  }

  CancelToken? canTripToken;
  bool isReqDataShowLoader = false;
  Future<dynamic> removeReqTrip(
    int? index,
    String id,
    List<BookingDetail> bookingList,
    BuildContext context, {
    bool isBack = false,
  }) async {
    if (_isClosed) return;

    canTripToken?.cancel();
    canTripToken = CancelToken();
    try {
      final url = EndPoints.cancelReqTrip(id);
      isReqDataShowLoader = true;
      notify();
      final response = await tripRepo.removeReqTrip(
        ApiRequest(path: url, cancelToken: canTripToken),
      );
      response.when(
        success: (data) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;

          if (index != null) bookingList.removeAt(index);

          /// remove trip data from trip list according to page index

          if (isBack) AppNavigationService.pop(context);
          notify();
        },
        error: (error) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      isReqDataShowLoader = false;
      notify();
    } catch (e) {
      if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
      isReqDataShowLoader = false;
      notify();
      'removeReqTrip error: $e'.logE;
    }
  }

  /// cancel booking detail
  Future<dynamic> removeBookingDetail(
    int id,
    int index,
    BuildContext context,
    String reason,
  ) async {
    if (_isClosed) return;

    canTripToken?.cancel();
    canTripToken = CancelToken();
    try {
      final url = EndPoints.customerBookingDetailCancel;
      isReqDataShowLoader = true;
      notify();
      final data = {
        AppStrings.bookingDetailStr: id,
        AppStrings.cancelReason: reason,
      };

      final response = await tripRepo.cancelSubBooking(
        ApiRequest(path: url, data: data, cancelToken: canTripToken),
      );
      response.when(
        success: (data) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          selectedTrip?.bookingDetails?.removeAt(index);
          context.l10n.tripCancelledSuccessfully.showSuccessAlert();
          // AppNavigationService.pop(context);
          notify();
        },
        error: (error) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      isReqDataShowLoader = false;
      notify();
    } catch (e) {
      if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
      isReqDataShowLoader = false;
      notify();
      'removeBookingDetail error: $e'.logE;
    }
  }

  /// cancel whole booking
  Future<dynamic> removeWholeBooking(
    String id,
    BuildContext context,
    String reason,
  ) async {
    if (_isClosed) return;

    canTripToken?.cancel();
    canTripToken = CancelToken();
    try {
      final url = EndPoints.customerBookingCancel;
      isReqDataShowLoader = true;
      notify();
      final data = {
        AppStrings.bookingName: int.tryParse(id),
        AppStrings.cancelReason: reason,
      };

      final response = await tripRepo.cancelWholeBooking(
        ApiRequest(path: url, data: data, cancelToken: canTripToken),
      );
      response.when(
        success: (data) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          context.l10n.bookingCancelledSuccessfully.showSuccessAlert();
          AppNavigationService.pop(context);
          notify();
        },
        error: (error) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      isReqDataShowLoader = false;
      notify();
    } catch (e) {
      if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
      isReqDataShowLoader = false;
      notify();
      'removeWholeBooking error: $e'.logE;
    }
  }

  /// cancel booked car
  Future<dynamic> removeBookedCar(
    String id,
    BuildContext context,
    String reason,
    int index,
  ) async {
    if (_isClosed) return;

    canTripToken?.cancel();
    canTripToken = CancelToken();
    try {
      final url = EndPoints.customerBookedCarCancel;
      isReqDataShowLoader = true;
      notify();
      final data = {
        AppStrings.bookedCar: int.tryParse(id),
        AppStrings.cancelReason: reason,
      };

      final response = await tripRepo.cancelBookedCar(
        ApiRequest(path: url, data: data, cancelToken: canTripToken),
      );
      response.when(
        success: (data) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          selectedTrip?.bookingDetails?[index].carDetails?.removeAt(index);
          context.l10n.carCancelledSuccessfully.showSuccessAlert();
          // AppNavigationService.pop(context);
          notify();
        },
        error: (error) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      isReqDataShowLoader = false;
      notify();
    } catch (e) {
      if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
      isReqDataShowLoader = false;
      notify();
      'removeBookedCar error: $e'.logE;
    }
  }

  /// Get payment summary details
  /// [id] is the trip ID
  /// [context] is the BuildContext
  /// [isExclusive] flag to indicate if trip is exclusive
  CancelToken? getPaymentSummaryToken;
  bool isPaymentSummaryLoad = false;
  PaymentSummaryData? paymentSummaryData;
  Future<dynamic> getRemainingPaymentSummary(
    String id,
    BuildContext context, {
    bool isExclusive = false,
  }) async {
    if (_isClosed) return;

    getPaymentSummaryToken?.cancel();
    getPaymentSummaryToken = CancelToken();
    try {
      final url = isExclusive
          ? EndPoints.exclusiveTripRemainingPaymentSummary
          : EndPoints.sharedTripRemainingPaymentSummary;
      isPaymentSummaryLoad = true;
      notify();
      final response = await tripRepo.getTripDetail(
        ApiRequest(path: '$url$id/', cancelToken: getPaymentSummaryToken),
      );

      if (_isClosed) return;

      response.when(
        success: (data) {
          if (_isClosed || (getPaymentSummaryToken?.isCancelled ?? true)) {
            return;
          }
          print('||||??? data = ${data['transporter'].toString()}');
          paymentSummaryData = PaymentSummaryData.fromJson(data);
          notify();
        },
        error: (error) {
          if (_isClosed || (getPaymentSummaryToken?.isCancelled ?? true)) {
            return;
          }
          error.message.showErrorAlert();
        },
      );

      isPaymentSummaryLoad = false;
      notify();
    } catch (e) {
      if (_isClosed || (getPaymentSummaryToken?.isCancelled ?? true)) return;
      isPaymentSummaryLoad = false;
      notify();
      'getPaymentSummary error: $e'.logE;
    }
  }

  /// this function is called to add notes
  CancelToken? deleteNotesToken;
  bool isDeleteNotesLoad = false;
  Future<dynamic> deleteNotes(
    String id,
    BuildContext context, {
    required Function() onSuccess,
  }) async {
    deleteNotesToken?.cancel();
    deleteNotesToken = CancelToken();
    try {
      if (_isClosed) return;
      final url = EndPoints.deleteNotes(id);
      isDeleteNotesLoad = true;
      notify();
      final response = await tripRepo.deleteNotes(
        ApiRequest(path: url, cancelToken: deleteNotesToken),
      );

      response.when(
        success: (data) {
          if (_isClosed || (deleteNotesToken?.isCancelled ?? true)) return;
          final tempList = <NoteModel>[
            ...selectedTrip?.bookingDetails?.firstOrNull?.notes ?? [],
          ]..removeWhere((element) => element.id.toString() == id);
          selectedTrip?.bookingDetails?.firstOrNull?.notes = tempList;
          onSuccess();
          context.l10n.bookingNoteDeletedSuccess.showSuccessAlert();
        },
        error: (error) {
          if (_isClosed || (deleteNotesToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      isDeleteNotesLoad = false;
      notify();
    } catch (e) {
      if (_isClosed || (deleteNotesToken?.isCancelled ?? true)) return;
      isDeleteNotesLoad = false;
      notify();
      'Delete note error: $e'.logE;
    }
  }

  /// to pay remaining amount
  CancelToken? payRemainingAmountToken;
  bool isPayRemainingAmountLoad = false;
  Future<dynamic> getRemainAmountSummary(
    String id,
    BuildContext context,
    String bookingId,
    TripDetailProvider tripDetailProvider, {
    bool isExclusive = false,
    int index = -1,
  }) async {
    payRemainingAmountToken?.cancel();
    payRemainingAmountToken = CancelToken();
    try {
      if (_isClosed) return;
      final url = isExclusive
          ? EndPoints.exclusiveTripRemainingPaymentSummary
          : EndPoints.sharedTripRemainingPaymentSummary;
      isPayRemainingAmountLoad = true;
      notify();
      final response = await tripRepo.getRemainAmountSummary(
        ApiRequest(path: '$url$id/', cancelToken: payRemainingAmountToken),
      );

      response.when(
        success: (data) {
          if (_isClosed || (payRemainingAmountToken?.isCancelled ?? true)) {
            return;
          }
          paymentData.value = PaymentSummaryData.fromJson(data);
          AppNavigationService.pushNamed(
            context,
            AppRoutes.tripsRemainPaymentScreen,
            extra: RemainPaymentParams(
              bookingId: bookingId,
              tripDetailProvider: tripDetailProvider,
              index: index,
              isExclusiveTrip: isExclusive,
            ),
          );
          notify();
        },
        error: (error) {
          if (_isClosed || (payRemainingAmountToken?.isCancelled ?? true)) {
            return;
          }
          error.message.showErrorAlert();
        },
      );

      isPayRemainingAmountLoad = false;
      notify();
    } catch (e) {
      if (_isClosed || (payRemainingAmountToken?.isCancelled ?? true)) return;
      isPayRemainingAmountLoad = false;
      notify();
      'pay remaining amount api error: $e'.logE;
    }
  }

  // pay settlement amount
  CancelToken? paySettlementAmountToken;
  bool isPaySettlementAmountLoad = false;
  Future<dynamic> getSettlementAmountSummary(
    BuildContext context,
    String bookingId, {
    bool isExclusive = false,
  }) async {
    paySettlementAmountToken?.cancel();
    paySettlementAmountToken = CancelToken();
    try {
      if (_isClosed) return;
      final url = isExclusive
          ? EndPoints.getExclusiveTripPaymentSettlementSummary(bookingId)
          : EndPoints.getSharedTripPaymentSettlementSummary(bookingId);
      isPaySettlementAmountLoad = true;
      notify();
      final response = await tripRepo.getSettlementAmount(
        ApiRequest(path: url, cancelToken: paySettlementAmountToken),
      );

      response.when(
        success: (data) {
          if (_isClosed || (paySettlementAmountToken?.isCancelled ?? true)) {
            return;
          }
          AppNavigationService.pushNamed(
            context,
            AppRoutes.tripsPaymentSettelementScreen,
            extra: PaymentSettlementParams(
              bookingId: bookingId,
              paymentSettlementData: data,
              isExclusiveTrip: isExclusive,
            ),
          );
          notify();
        },
        error: (error) {
          if (_isClosed || (paySettlementAmountToken?.isCancelled ?? true)) {
            return;
          }
          error.message.showErrorAlert();
        },
      );

      isPaySettlementAmountLoad = false;
      notify();
    } catch (e) {
      if (_isClosed || (paySettlementAmountToken?.isCancelled ?? true)) return;
      isPaySettlementAmountLoad = false;
      notify();
      'pay settlement amount api error: $e'.logE;
    }
  }

  /// get payment information
  void navigateToWebviewForPayment(
    BuildContext context, {
    bool isExclusiveTrip = false,
    required String bookingId,
  }) => AppNavigationService.pushNamed(
    context,
    AppRoutes.commonWebViewScreen,
    extra: CommonWebViewParams(
      context: context,
      bodyData: {'booking_detail': bookingId},
      isRemainAmountAPI: true,
      isExclusiveTrip: isExclusiveTrip,
      bookingId: bookingId,
    ),
  );

  void clearChatCountNAssignModel(
    BuildContext context, {
    required int chatIndex,
    required int index,
    required int chatRoomId,
    required bool isActive,
    required bool isCount,
  }) {
    final chatRoomModel = ChatRoomDetailModel(
      id: chatRoomId,
      isActive: isActive,
      unreadMessageCount: 0,
    );

    final tripDetailProvider = context.read<TripDetailProvider>();
    if (isCount) {
      switch (chatIndex) {
        case 0:
          tripDetailProvider
                  .selectedTrip
                  ?.bookingDetails?[index]
                  .sharedBookings
                  ?.firstOrNull
                  ?.startLocationChatRoomDetail
                  ?.unreadMessageCount =
              0;
        case 1:
          tripDetailProvider
                  .selectedTrip
                  ?.bookingDetails?[index]
                  .sharedBookings
                  ?.firstOrNull
                  ?.endLocationChatRoomDetail
                  ?.unreadMessageCount =
              0;

        default:
          tripDetailProvider
                  .selectedTrip
                  ?.bookingDetails?[index]
                  .driverChatRoomDetail
                  ?.unreadMessageCount =
              0;
      }
    } else {
      switch (chatIndex) {
        case 0:
          tripDetailProvider
                  .selectedTrip
                  ?.bookingDetails?[index]
                  .sharedBookings
                  ?.firstOrNull
                  ?.startLocationChatRoomDetail =
              chatRoomModel;
        case 1:
          tripDetailProvider
                  .selectedTrip
                  ?.bookingDetails?[index]
                  .sharedBookings
                  ?.firstOrNull
                  ?.endLocationChatRoomDetail =
              chatRoomModel;

        default:
          tripDetailProvider
                  .selectedTrip
                  ?.bookingDetails?[index]
                  .driverChatRoomDetail =
              chatRoomModel;
      }
    }
    tripDetailProvider.notify();
  }

  String formateAffectedTime(String? affectedTime, BuildContext context) {
    final parts = affectedTime?.split(' ');

    if (parts != null && parts.isNotEmpty && parts.length == 2) {
      final day = parts[0];
      final hour = parts[1].split(':')[0];
      return hour == '00'
          ? '$day ${context.l10n.day}'
          : '$day ${context.l10n.day} $hour ${context.l10n.hour}';
    } else if (parts != null && parts.isNotEmpty && parts.length == 1) {
      final hour = parts[0].split(':')[0];
      return hour == '00' ? '' : '$hour ${context.l10n.hour}';
    }
    return '';
  }

  void navigateToUserInfoScreen(
    BuildContext context,
    Function(List<AssigneeModel>? assigneeModel) onSuccess,
  ) {
    final tripDropUserInfoData = selectedTrip?.bookingAssignee?.firstOrNull;
    final tripPickupUserInfoData = selectedTrip?.bookingAssignee?.lastOrNull;
    AppNavigationService.pushNamed(
      context,
      AppRoutes.tripsUserInfoScreen,
      extra: TripUserInfoParams(
        isEdit: !(selectedTrip?.bookingAssignee?.isNotEmpty ?? false),
        tripId: selectedTrip?.id?.toString(),
        dropUserName: tripDropUserInfoData?.name ?? '',
        dropUserImage: tripDropUserInfoData?.idProofImageUrl ?? '',
        dropUserDocType: tripDropUserInfoData?.idProofType ?? '',
        pickUserName: tripPickupUserInfoData?.name ?? '',
        pickUserImage: tripPickupUserInfoData?.idProofImageUrl ?? '',
        pickUserDocType: tripPickupUserInfoData?.idProofType ?? '',
        onSuccess: onSuccess,
      ),
    );
  }

  @override
  void dispose() {
    payRemainingAmountToken?.cancel();
    deleteNotesToken?.cancel();
    getPaymentSummaryToken?.cancel();
    getTripDetailsToken?.cancel();
    canTripToken?.cancel();
    paySettlementAmountToken?.cancel();
    cancelBookingOrTripOrCarReasonController.dispose();
    super.dispose();
  }
}
