// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get helloWorld => 'hola mundo';

  @override
  String get byeWorld => 'adiós mundo';

  @override
  String get just_now => 'Ahora mismo';

  @override
  String get min_ago => 'Minuto atrás';

  @override
  String get hour_ago => 'Hora atrás';

  @override
  String get day_ago => 'Día atrás';

  @override
  String get week_ago => 'Semana atrás';

  @override
  String get year_ago => 'Año atrás';

  @override
  String get today => 'Hoy';

  @override
  String get yesterday => 'Ayer';

  @override
  String get copied => 'Copiado';

  @override
  String get pleaseEnterEmail => 'Por favor ingrese el correo electrónico';

  @override
  String get pleaseEnterValidEmail =>
      'Por favor ingrese un correo electrónico válido';

  @override
  String get pleaseEnterCurrentPassword =>
      'Por favor ingrese la contraseña actual';

  @override
  String get pleaseEnterPassword => 'Por favor ingrese la contraseña';

  @override
  String get passwordMinLength =>
      'La contraseña debe tener al menos 8 caracteres';

  @override
  String get passwordShouldBeAtLeast8Characters =>
      'La contraseña debe tener al menos 8 caracteres.';

  @override
  String get newPasswordAndOldPasswordCannotBeSame =>
      'La nueva contraseña y la contraseña anterior no pueden ser iguales';

  @override
  String get otpMinLength => 'Por favor ingrese un OTP válido';

  @override
  String get pleaseEnterValidUsername =>
      'Por favor ingrese un nombre de usuario válido';

  @override
  String get usernameMinLength =>
      'Por favor ingrese un nombre de usuario válido';

  @override
  String get pleaseEnterName => 'Por favor ingrese el nombre';

  @override
  String get nameMinLength => 'Por favor ingrese el nombre';

  @override
  String get pleaseEnterFirstName => 'Por favor ingrese el nombre';

  @override
  String get firstNameMinLength => 'Por favor ingrese el nombre';

  @override
  String get pleaseEnterLastName => 'Por favor ingrese el apellido';

  @override
  String get lastNameMinLength => 'Por favor ingrese el apellido';

  @override
  String get passwordDoesNotMatch => 'Las contraseñas no coinciden';

  @override
  String get accountRegisterSuccessfully =>
      'Cuenta registrada exitosamente, por favor verifique su correo electrónico.';

  @override
  String get passwordIncludeLetterNumberSymbol =>
      'La contraseña debe incluir al menos una letra mayúscula, una letra minúscula, un número y un carácter especial.';

  @override
  String get checkYourEmail => 'Revise su correo electrónico';

  @override
  String get sentVerificationCode =>
      'Hemos enviado un código de verificación a ';

  @override
  String get verifyOtp => 'Verificar OTP';

  @override
  String get pleaseEnterOtp => '¡Por favor ingrese el OTP!';

  @override
  String get pleaseEnterValidOtp => '¡Por favor ingrese un OTP válido!';

  @override
  String get didNtReceiveEmail => '¿No recibió el correo electrónico? ';

  @override
  String get clickToResend => 'Haga clic para reenviar';

  @override
  String get resetPasswordInstructions =>
      'Te enviaremos un código OTP a tu correo electrónico';

  @override
  String get sendAnEmail => 'Enviar un correo electrónico';

  @override
  String get ultimateStressFreeCarTransport =>
      'La experiencia definitiva de transporte de autos sin estrés';

  @override
  String get email => 'Correo electrónico';

  @override
  String get enterYourEmail => 'Ingrese su correo electrónico';

  @override
  String get password => 'Contraseña';

  @override
  String get enterYourPassword => 'Ingrese su contraseña';

  @override
  String get enterYourConfirmPassword =>
      'Ingresa tu contraseña de confirmación';

  @override
  String get forgotPassword => 'Olvidé mi contraseña';

  @override
  String get forgotPasswordQuestion => '¿Olvidó su contraseña?';

  @override
  String get logIn => 'Iniciar sesión';

  @override
  String get doNtHaveAccount => '¿No tiene una cuenta?';

  @override
  String get signUp => 'Registrarse';

  @override
  String get setPassword => 'Establecer contraseña';

  @override
  String get setNewPassword => 'Establezca una nueva contraseña';

  @override
  String get confirmPassword => 'Confirmar contraseña';

  @override
  String get pleaseEnterConfirmPassword =>
      'Por favor ingrese la confirmación de contraseña';

  @override
  String get pleaseEnterDetails =>
      'Por favor ingrese sus datos para crear una cuenta';

  @override
  String get name => 'Nombre';

  @override
  String get enterYourName => 'Ingrese su nombre';

  @override
  String get createAccount => 'Crear cuenta';

  @override
  String get alreadyHaveAccount => '¿Ya tiene una cuenta?';

  @override
  String get customer_support => 'Soporte al cliente';

  @override
  String get payments => 'Pagos';

  @override
  String get past_purchase => 'Compras anteriores';

  @override
  String get sign_out => 'Cerrar sesión';

  @override
  String get delete_account => 'Eliminar cuenta';

  @override
  String get edit_details => 'Editar detalles';

  @override
  String get change_password => 'Cambiar contraseña';

  @override
  String get save => 'Guardar';

  @override
  String get enter_your_name => 'Ingrese su nombre';

  @override
  String get enter_your_email => 'Ingrese su correo electrónico';

  @override
  String get enter_your_password => 'Ingrese su contraseña';

  @override
  String get set_new_password => 'Establecer nueva contraseña';

  @override
  String get go_ahead_and_set_a_new_password =>
      'Establezca una nueva contraseña';

  @override
  String get old_password => 'Contraseña anterior';

  @override
  String get uploadCarImg => 'Subir imágenes del auto';

  @override
  String get new_password => 'Nueva contraseña';

  @override
  String get confirm_password => 'Confirmar contraseña';

  @override
  String get please_enter_confirm_password =>
      'Por favor ingrese la confirmación de contraseña';

  @override
  String get transporter => 'Transportista';

  @override
  String get total_trip_cost => 'Costo total del viaje';

  @override
  String get no_of_vehicles => 'Número de vehículos';

  @override
  String get equipment_type => 'Tipo de equipo';

  @override
  String get vehicles_info => 'Información de vehículos';

  @override
  String get details => 'Detalles';

  @override
  String get car_brand => 'Marca del auto';

  @override
  String get car_model => 'Modelo del auto';

  @override
  String get car_serial => 'Número de serie del auto';

  @override
  String get car_year => 'Año del auto';

  @override
  String get view_details => 'Ver detalles';

  @override
  String get stockLocations => 'Ubicaciones de stock';

  @override
  String get stockLocation => 'Ubicación de stock';

  @override
  String get originStockLocation => 'Ubicación de stock de origen';

  @override
  String get chooseOriginStockLocation =>
      'Elija la ubicación de stock de origen';

  @override
  String get pleaseSelectPickupDate =>
      'Por favor seleccione la fecha de recogida';

  @override
  String get pleaseSelectDeliveryDate =>
      'Por favor seleccione la fecha de entrega';

  @override
  String get pleaseSelectDelivery => 'Por favor seleccione la fecha de entrega';

  @override
  String get dropStockLocation => 'Ubicación de stock de entrega';

  @override
  String get chooseDropStockLocation =>
      'Elija la ubicación de stock de entrega';

  @override
  String get vehicleInfo => 'Información de vehículos';

  @override
  String get vehicleBrand => 'Marca del vehículo';

  @override
  String get chooseVehicleBrand => 'Elija la marca del vehículo';

  @override
  String get vehicleModel => 'Modelo del vehículo';

  @override
  String get chooseVehicleModel => 'Elija el modelo del vehículo';

  @override
  String get vehicleYear => 'Año del vehículo';

  @override
  String get chooseVehicleYear => 'Elija el año del vehículo';

  @override
  String get vehicleCondition => 'Condición del vehículo';

  @override
  String get chooseVehicleCondition => 'Escriba la condición del vehículo';

  @override
  String get pleaseDescribeTheIssue => 'Por favor describa el problema';

  @override
  String get writeIssueDetailsHere => 'Escriba los detalles del problema aquí';

  @override
  String get vehicleSerialNo => 'Número de serie del vehículo';

  @override
  String get vehicleSerialNumber => 'Número de serie del vehículo';

  @override
  String get iNeedMyCarToBePickedUpAndTakenToTheStockLocation =>
      'Necesito que mi auto sea recogido y llevado a la ubicación de stock';

  @override
  String get enterPickupAddress => 'Ingrese la dirección de recogida';

  @override
  String get towingCost => 'Costo de remolque';

  @override
  String get drivingCostWithOperator => 'Costo de conducción con operador';

  @override
  String get addAnotherVehicle => 'Agregar otro vehículo';

  @override
  String get pickupAndDeliveryDates => 'Fechas de recogida y entrega';

  @override
  String get pickupDate => 'Fecha de recogida';

  @override
  String get deliveryDate => 'Fecha de entrega';

  @override
  String get transportAllVehicleInOneTruck =>
      'Transportar todos los vehículos en un camión';

  @override
  String get vehicleVersion => 'Versión del vehículo';

  @override
  String get passChanged => 'Contraseña cambiada exitosamente';

  @override
  String get findTransporter => 'Encontrar transportista';

  @override
  String get enterVBrandName => 'Ingrese la marca de su vehículo';

  @override
  String enterBrandName(String enterBrandName) {
    return 'Ingrese la marca de su vehículo $enterBrandName';
  }

  @override
  String get chooseVBrandName => 'Elija la marca de su vehículo';

  @override
  String chooseBrandName(String brandName) {
    return 'Elija la marca de su vehículo $brandName';
  }

  @override
  String get enterVYear => 'Ingrese el año de su vehículo';

  @override
  String enterVehicleYear(String enterVehicleYear) {
    return 'Ingrese el año de su vehículo $enterVehicleYear';
  }

  @override
  String get chooseVYear => 'Elija el año de su vehículo';

  @override
  String pleaseChooseVehicleYear(String vehicleYear) {
    return 'Elija el año de su vehículo $vehicleYear';
  }

  @override
  String get enterVModel => 'Ingrese el modelo de su vehículo';

  @override
  String enterVehicleModel(String enterVehicleModel) {
    return 'Ingrese el modelo de su vehículo $enterVehicleModel';
  }

  @override
  String get chooseVModel => 'Elija el modelo de su vehículo';

  @override
  String pleaseChooseVehicleModel(String vehicleModel) {
    return 'Elija el modelo de su vehículo $vehicleModel';
  }

  @override
  String get pleaseEnterVSerialNumber =>
      'Por favor ingrese el número de serie de su vehículo';

  @override
  String pleaseEnterSerialNumber(String serialNumber) {
    return 'Por favor ingrese el número de serie de su vehículo $serialNumber';
  }

  @override
  String get pleasePickUpAddress =>
      'Por favor ingrese su dirección de recogida';

  @override
  String get bookingDataVerification =>
      'Los datos de la reserva han sido enviados exitosamente para verificación del auto';

  @override
  String get exclusiveCreated => 'Reserva exclusiva creada exitosamente';

  @override
  String get pleasePickUpZip =>
      'Por favor ingrese el código postal de su dirección de recogida';

  @override
  String get pleaseEnterDropUserName =>
      'Por favor ingrese el nombre del usuario de entrega';

  @override
  String get invalidDropUserName =>
      'Por favor ingrese un nombre de usuario válido';

  @override
  String get pleaseEnterDropUserDocType =>
      'Por favor ingrese el tipo de documento del usuario de entrega';

  @override
  String get pleaseEnterDropUserDoc =>
      'Por favor ingrese el documento del usuario de entrega';

  @override
  String get pleaseEnterPickUserName =>
      'Por favor ingrese el nombre del usuario de recogida';

  @override
  String get invalidPickUserName =>
      'Por favor ingrese un nombre de usuario de selección válido';

  @override
  String get pleaseEnterPickUserDocType =>
      'Por favor ingrese el tipo de documento del usuario de recogida';

  @override
  String get signOut => 'Cerrar sesión';

  @override
  String get cancel => 'Cancelar';

  @override
  String get newUpdateAvailable => 'Nueva actualización disponible';

  @override
  String get updateMessage =>
      'Hemos realizado mejoras y corregido errores para mejorar tu experiencia. Actualiza la aplicación para seguir usándola sin problemas.';

  @override
  String versionInfo(String currentAppVersion, String minimumAppVersion) {
    return '📱 Versión actual: $currentAppVersion\n✅  Versión requerida: $minimumAppVersion';
  }

  @override
  String get updateNow => 'Actualizar ahora';

  @override
  String get troubleUpdating =>
      '¿Tiene problemas para actualizar?\n Visita la tienda de aplicaciones manualmente o contacta con el soporte técnico';

  @override
  String get signOutContent => '¿Está seguro de que desea cerrar sesión?';

  @override
  String get deleteAccount => 'Eliminar cuenta';

  @override
  String get deleteAccountContent =>
      '¿Está seguro de que desea eliminar esta cuenta?';

  @override
  String get delete => 'Eliminar';

  @override
  String get searchOriginLocation => 'Buscar ubicación de origen';

  @override
  String get searchDropLocation => 'Buscar ubicación de entrega';

  @override
  String get enterZipCode => 'Ingrese el código postal';

  @override
  String get userPickUpLocation => 'Ubicación de recogida del usuario';

  @override
  String get userLocation => 'Ubicación del usuario';

  @override
  String get userDeliveryLocation => 'Ubicación de entrega del usuario';

  @override
  String get toContinueWithTrip =>
      'Para continuar con el viaje exclusivo debe completar la ubicación de recogida y entrega';

  @override
  String get otpSentSuccess => '¡OTP enviado exitosamente!';

  @override
  String get assigned => 'Asignado';

  @override
  String get spotAvailableReservation => 'Lugar disponible para reserva';

  @override
  String get noOfVehicle => 'Número de vehículos';

  @override
  String get vehicleType => 'Tipo de vehículo';

  @override
  String get enterVehicleBrand => 'Ingrese la marca del vehículo';

  @override
  String get enterVehicleYearLabel => 'Ingrese el año del vehículo';

  @override
  String get enterVehicleModelLabel => 'Ingrese el modelo del vehículo';

  @override
  String get exclusiveTrip => 'Viaje exclusivo';

  @override
  String get didNtFoundCar =>
      '¿No encontró su auto? Envíe la información de su auto para verificación';

  @override
  String get aPersonalized =>
      'Un servicio personalizado donde su vehículo es recogido en sus instalaciones y entregado en su ubicación elegida (si es accesible). Sin compartir y con programación flexible, Ofrece exclusividad a un costo más alto';

  @override
  String get pleaseEnterPickUserDoc =>
      'Por favor ingrese el documento del usuario de recogida';

  @override
  String get sendRequest => 'Enviar solicitud';

  @override
  String get sharedTrip => 'Viaje compartido';

  @override
  String get transportUrVehicle =>
      'Transporte su vehículo con otros dejándolo en un punto de recogida designado para entrega en una ubicación establecida. Esta opción económica excluye la recogida y entrega a domicilio';

  @override
  String get winchRequired => 'Se requiere grúa';

  @override
  String get noTransportersFound => 'No se encontraron transportistas';

  @override
  String get noProvider => 'Sin proveedor';

  @override
  String get thereIsNoProviderWaitList =>
      'No hay proveedor con las características solicitadas, Puede unirse a la lista de espera o continuar con un viaje exclusivo';

  @override
  String get thereIsNoProvider =>
      'No hay proveedor con las características solicitadas, Puede unirse continuar con un viaje exclusivo';

  @override
  String get enterPersonName => 'Ingrese el nombre de la persona';

  @override
  String get chooseIdType => 'Elija el tipo de identificación';

  @override
  String get idType => 'Tipo de identificación';

  @override
  String get idProofPhoto => 'Foto de identificación';

  @override
  String get uploadPicture => 'Suba fotos de su identificación';

  @override
  String get tapThisAnd =>
      'Toque esto y elija las fotos que desea subir desde su dispositivo';

  @override
  String get enterUrAddress => 'Ingrese su dirección';

  @override
  String get loading => 'Cargando...';

  @override
  String get yourAddress => 'No se encontró su dirección';

  @override
  String get summary => 'Resumen';

  @override
  String get transportationCost => 'Costo de transporte';

  @override
  String get dropOffStorageFee => 'Tarifa de almacenamiento de entrega';

  @override
  String get pickupStorageFee => 'Tarifa de almacenamiento de recogida';

  @override
  String get serviceFee => 'Tarifa de servicio';

  @override
  String get dropTransportation => 'Costo del transporte de entrega';

  @override
  String get includeInsurance => 'Incluir seguro';

  @override
  String get noteEnsure =>
      'Nota: Asegure su vehículo contra cualquier daño durante el transporte';

  @override
  String get total => 'Total';

  @override
  String noteUHaveToPay(String pay, String remain) {
    return 'Nota: Debe pagar el $pay% del monto total ahora al momento de la reserva y el $remain% restante al momento de la entrega';
  }

  @override
  String get actualTransportationCost => 'Costo real de transporte';

  @override
  String get extraTransportationCost => 'Costo extra de transporte';

  @override
  String get insuranceCost => 'Costo del seguro';

  @override
  String get totalInsuranceCost => 'Costo total del seguro';

  @override
  String get exclusiveTripExtraCostNote =>
      'Debido a que es un viaje exclusivo, Debe pagar un costo extra de transporte';

  @override
  String get grossAmount => 'Monto Bruto';

  @override
  String get netAmount => 'Monto Neto';

  @override
  String get startLocationStorageFee =>
      'Tarifa de Almacenamiento de Ubicación de Inicio';

  @override
  String get endLocationStorageFee =>
      'Tarifa de Almacenamiento de Ubicación Final';

  @override
  String get customerLocationToStartLocationServiceFee =>
      'Tarifa de Servicio de Ubicación del Cliente a Ubicación de Inicio';

  @override
  String get netTransportationCost => 'Costo Neto de Transporte';

  @override
  String get netStartLocationStorageFee =>
      'Tarifa Neta de Almacenamiento de Ubicación de Inicio';

  @override
  String get netEndLocationStorageFee =>
      'Tarifa Neta de Almacenamiento de Ubicación Final';

  @override
  String get netCustomerLocationToStartLocationServiceFee =>
      'Tarifa Neta de Servicio de Ubicación del Cliente a Ubicación de Inicio';

  @override
  String get netInsuranceCost => 'Costo Neto de Seguro';

  @override
  String get netTripCharge => 'Cargo Neto del Viaje';

  @override
  String get totalTripCost => 'Costo Total del Viaje';

  @override
  String get netTotalAppFee => 'Tarifa Neta Total de la App';

  @override
  String get totalNetTransportationCharge => 'Cargo Neto Total de Transporte';

  @override
  String get totalNetInsuranceCharge => 'Cargo Neto Total de Seguro';

  @override
  String get totalNetBookingPay => 'Pago Neto Total de Reserva';

  @override
  String get transportationCostExplanation =>
      'Costo por transportar su vehículo desde el punto de recogida hasta el lugar de entrega';

  @override
  String get storageCostExplanation =>
      'Tarifa de almacenamiento por mantener su vehículo en nuestras instalaciones';

  @override
  String get insuranceCostExplanation =>
      'Cobertura de seguro opcional para proteger su vehículo durante el transporte';

  @override
  String get platformFeeExplanation =>
      'Tarifa de servicio por usar nuestra plataforma y gestión de reservas';

  @override
  String get dropOffServiceExplanation =>
      'Servicio adicional para recoger su vehículo desde su ubicación';

  @override
  String get daysSelected => 'Días seleccionados';

  @override
  String get perDayRate => 'Tarifa por día';

  @override
  String get totalDays => 'Total de días';

  @override
  String get baseRate => 'Tarifa base';

  @override
  String get discountApplied => 'Descuento aplicado';

  @override
  String get taxesAndFees => 'Impuestos y tarifas';

  @override
  String get youPayNow => 'Usted paga ahora para confirmar la reserva';

  @override
  String get remainingOnDelivery => 'Monto restante a pagar en la entrega';

  @override
  String costBreakdownFor(String companyName) {
    return 'Desglose de costos para $companyName';
  }

  @override
  String get finalAmountToPay => 'Monto final a pagar ahora';

  @override
  String get viewCarCharges => 'Ver Cargos del Vehículo';

  @override
  String carChargesFor(String serialNumber) {
    return 'Cargos del Vehículo para $serialNumber';
  }

  @override
  String get totalCost => 'Costo Total';

  @override
  String get taxAmount => 'Monto del Impuesto';

  @override
  String get taxRate => 'Tasa de Impuesto';

  @override
  String get totalWithTax => 'Total con Impuesto';

  @override
  String get netTotalWithTax => 'Total Neto con Impuesto';

  @override
  String get providerBreakdown => 'Desglose por Proveedor';

  @override
  String get overallSummary => 'Resumen General';

  @override
  String get costBreakdown => 'Desglose de Costos';

  @override
  String get transportationCostDescription =>
      'Costo por transportar vehículos desde la recogida hasta la entrega';

  @override
  String get startLocationStorage => 'Almacenamiento en Ubicación de Inicio';

  @override
  String get startLocationStorageDescription =>
      'Tarifa de almacenamiento en el lugar de recogida';

  @override
  String get endLocationStorage => 'Almacenamiento en Ubicación Final';

  @override
  String get endLocationStorageDescription =>
      'Tarifa de almacenamiento en el lugar de entrega';

  @override
  String get customerLocationService => 'Servicio de Ubicación del Cliente';

  @override
  String get customerLocationServiceDescription =>
      'Servicio para recoger desde su ubicación';

  @override
  String get insuranceCoverage => 'Cobertura de Seguro';

  @override
  String get insuranceCoverageDescription =>
      'Protección para sus vehículos durante el transporte';

  @override
  String vehicleWiseCharges(int count) {
    return 'Cargos por Vehículo ($count vehículos)';
  }

  @override
  String get vehicle => 'Vehículo';

  @override
  String get transportation => 'Transporte';

  @override
  String get startStorage => 'Almacenamiento Inicial';

  @override
  String get endStorage => 'Almacenamiento Final';

  @override
  String get customerService => 'Servicio al Cliente';

  @override
  String get insurance => 'Seguro';

  @override
  String get netYouPay => 'Neto (Usted Paga)';

  @override
  String get finalAmountToPayTitle => 'Monto Final a Pagar';

  @override
  String get payNowToConfirm => 'Pague ahora para confirmar su reserva';

  @override
  String get tax => 'Impuesto';

  @override
  String get totalAmountToPay => 'Monto Total a Pagar';

  @override
  String get providerBreakdownTitle => 'Desglose por Proveedor';

  @override
  String get tripId => 'ID de Viaje';

  @override
  String get failedToFetchSuggestion => 'Error al obtener sugerencias';

  @override
  String get shipmentConfirmation => 'Confirmación de envío';

  @override
  String get yourShipment => 'Su envío';

  @override
  String get noOfTotalVehicle => 'Número total de vehículos';

  @override
  String get proceedToPayment => 'Proceder al pago';

  @override
  String get pickupFrom => 'Recoger desde';

  @override
  String get dropAt => 'Entregar en';

  @override
  String get viewDetails => 'Ver detalles';

  @override
  String get pickupLocation => 'Ubicación de recogida';

  @override
  String get deliveryLocation => 'Ubicación de entrega';

  @override
  String get notePleaseDrop =>
      'Nota: Por favor deje su vehículo un día antes de la fecha programada y recójalo un día después de la entrega para permitir tiempo para una lista de verificación de condición';

  @override
  String get pleaseSelectDropDate =>
      'Por favor seleccione la fecha de entrega de su vehículo en la ubicación de stock';

  @override
  String get pleaseSelectDropDateForAllVehicles =>
      'Por favor seleccione la fecha de entrega de su vehículo en la ubicación de stock para todos los vehículos';

  @override
  String get carBrand => 'Marca del auto';

  @override
  String get carModel => 'Modelo del auto';

  @override
  String get carSerial => 'Número de serie del auto';

  @override
  String get carYear => 'Año del auto';

  @override
  String get carSize => 'Tamaño del auto';

  @override
  String get dropOffDate => 'Fecha de entrega';

  @override
  String get selectVehicleDrop =>
      'Seleccione la fecha de entrega del vehículo en la ubicación de stock';

  @override
  String get selectDateForPickupCar =>
      'Elige la fecha en que tu vehículo estará listo para recoger';

  @override
  String get storageFee => 'Tarifa de almacenamiento';

  @override
  String get perDay => 'por día';

  @override
  String get close => 'Cerrar';

  @override
  String get continues => 'Continuar';

  @override
  String get transportList => 'Lista de transportistas';

  @override
  String get edit => 'Editar';

  @override
  String get chooseTransporter => 'Elija transportista';

  @override
  String get filterBy => 'Filtrar por';

  @override
  String get noProviderFound => 'No se encontró proveedor';

  @override
  String get userInfo => 'Información del usuario';

  @override
  String get carDropPerson => 'Información de la persona que entrega el auto';

  @override
  String get thisIsDropInfo =>
      'Esta es la información de la persona que va a entregar el auto';

  @override
  String get carPickupPerson => 'Información de la persona que recoge el auto';

  @override
  String get thisIsDeliveryInfo =>
      'Esta es la información de la persona que va a recibir la entrega';

  @override
  String get uAlreadyAssign =>
      'Ya asignó este vehículo, por favor seleccione otro';

  @override
  String get noSlotAvailable =>
      'No hay espacio disponible para su próximo auto';

  @override
  String get pleaseAssignCar => 'Por favor asigna un auto para el espacio';

  @override
  String get pleaseChooseCar => 'Por favor elija un auto para el espacio';

  @override
  String get clearAll => 'Limpiar todo';

  @override
  String get teamCapacity => 'Capacidad del equipo';

  @override
  String get rating => 'Calificación';

  @override
  String get rated => 'Calificado';

  @override
  String get pricing => 'Precios';

  @override
  String get lowestPerKM => 'Más bajo por KM';

  @override
  String get in2Days => 'En 2 días';

  @override
  String get rateProvider => 'Calificar proveedor';

  @override
  String get payRemainAmount => 'Pagar monto restante';

  @override
  String get paySettlementAmount => 'Pagar monto de liquidación';

  @override
  String get paymentSettlement => 'Liquidación de pago';

  @override
  String get remainPayments => 'Pagos restantes';

  @override
  String get cancelTrip => 'Cancelar viaje';

  @override
  String get inTrackTransport => 'Transporte en pista';

  @override
  String get addNotes => 'Notas';

  @override
  String get notes => 'Notas';

  @override
  String get pleaseAddNotes => 'Por favor, Agrega notas';

  @override
  String get reportsFromTransporter => 'Informes del transportista';

  @override
  String get writeNotes => 'Escribir notas';

  @override
  String get writeUrMessage => 'Escriba su mensaje aquí';

  @override
  String get upcoming => 'Próximos';

  @override
  String get ongoing => 'En curso';

  @override
  String get completed => 'Completados';

  @override
  String get chatWithProvider => 'Chatear con el proveedor';

  @override
  String get checklist => 'Lista de verificación';

  @override
  String get checklists => 'Listas de verificación';

  @override
  String get mileageAtPickup => 'Kilometraje al recoger';

  @override
  String get mileageAtDelivery => 'Kilometraje al entregar';

  @override
  String get pickupDateNPlace => 'Fecha y lugar de recogida';

  @override
  String get deliveryDateNPlace => 'Fecha y lugar de entrega';

  @override
  String get pickupOfficer => 'Oficial de recogida';

  @override
  String get deliveryOfficer => 'Oficial de entrega';

  @override
  String get yes => 'Sí';

  @override
  String get no => 'No';

  @override
  String get uploadPictureOfId => 'Subir fotos de su identificación';

  @override
  String get tapThisNChoose =>
      'Toque esto y elija las fotos que desea subir desde su dispositivo';

  @override
  String get editDetails => 'Editar detalles';

  @override
  String get originLocation => 'Ubicación de origen';

  @override
  String get dropLocation => 'Ubicación de entrega';

  @override
  String get addMoreCars => 'Agregar más autos';

  @override
  String get paymentSummary => 'Resumen de pago';

  @override
  String get paidAmount => 'Monto pagado';

  @override
  String get remainAmount => 'Monto restante';

  @override
  String get remainAmountTax => 'Impuesto sobre el importe restante';

  @override
  String get cancelBooking => 'Cancelar reserva';

  @override
  String get chooseWhatUFeel => 'Elija lo que siente';

  @override
  String get writeSuggestion => 'Escribir sugerencia';

  @override
  String get writeUrSuggestion => 'Escriba su sugerencia si tiene alguna';

  @override
  String get submitReview => 'Enviar reseña';

  @override
  String get viewCheckList => 'Ver lista de verificación';

  @override
  String get removeCar => 'Eliminar auto';

  @override
  String get areUSureRemoveCar =>
      '¿Está seguro de que desea eliminar este auto?';

  @override
  String get noVehicleAvailable => 'No hay vehículo disponible';

  @override
  String get pleaseEnterPickLocation =>
      'Por favor ingrese su ubicación de recogida';

  @override
  String get pleaseEnterDeliveryLocation =>
      'Por favor ingrese su ubicación de entrega';

  @override
  String get submitForApproval => 'Enviar detalles para aprobación';

  @override
  String get assignedCars => 'Autos asignados';

  @override
  String get howManyCar => '¿Cuántos autos desea enviar con este proveedor?';

  @override
  String get chooseVehicle => 'Elegir vehículo';

  @override
  String get assignMoreCar => 'Asignar más autos';

  @override
  String get user => 'Usuario';

  @override
  String get userEmail => 'Correo electrónico del usuario';

  @override
  String get creditDebitCards => 'Tarjetas de crédito y débito';

  @override
  String get bank => 'Banco';

  @override
  String get addAnotherMethod => 'Agregar otro método';

  @override
  String get congratulation => 'Felicitaciones';

  @override
  String get youHaveGotTransportation =>
      'Ha obtenido el lugar de transporte. Puede continuar realizando los pagos';

  @override
  String get changeBid => 'Cambiar oferta';

  @override
  String get yourCurrentBid => 'Su oferta actual';

  @override
  String get minimumBidRequired =>
      'Oferta mínima requerida para que su auto sea transportado';

  @override
  String get minimumRequirementFor =>
      'Requisito mínimo para que su auto sea transportado';

  @override
  String get chooseYourBidIncrement => 'Elija su incremento de oferta';

  @override
  String get yourNeWBidWillBe => 'Su nueva oferta será';

  @override
  String get note => 'Nota';

  @override
  String get requestedTrips => 'Viajes solicitados';

  @override
  String get acceptedTrips => 'Viajes aceptados';

  @override
  String get yourPositionInList => 'Su posición en la lista';

  @override
  String get enterAuction => 'Ingresar a subasta';

  @override
  String get reject => 'Rechazar';

  @override
  String get dueToMoreUser =>
      'Debido a que hay más usuarios que lugares disponibles, se dará prioridad para reservar lugares a aquellos que ofrezcan un pago más alto por los lugares';

  @override
  String get wait => 'Esperar';

  @override
  String get joinWaitlist => 'Unirse a lista de espera';

  @override
  String get areUSure => '¿Está seguro?';

  @override
  String get uAreRejecting =>
      'Está rechazando esta oferta, ¿Desea esperar otra oferta o unirse a la lista de espera?';

  @override
  String get resumeTrip => 'Reanudar viaje';

  @override
  String get editTrip => 'Editar viaje';

  @override
  String get noTripFound => 'No se encontró viaje';

  @override
  String get auction => 'Subasta';

  @override
  String get closingIn => 'Cierra en';

  @override
  String get opnTrackTransportation => 'Transporte en pista';

  @override
  String get availableSlot => 'Espacios disponibles';

  @override
  String get noOfAvailableSlot => 'Número de ranuras disponibles';

  @override
  String get carsWanted => 'Autos que desea mover';

  @override
  String get yourCurrentSpot => 'Su lugar actual';

  @override
  String get listOfCurrentBid => 'Lista de ofertas actuales';

  @override
  String get theUsersListed =>
      'Los usuarios listados dentro del cuadro son los que serán transportados';

  @override
  String get exitAuction => 'Salir de subasta';

  @override
  String get startBiding => 'Comenzar ofertas';

  @override
  String get uHaveNotBid => 'No ha realizado ninguna oferta';

  @override
  String get accept => 'Aceptar';

  @override
  String get origin => 'Origen';

  @override
  String get destination => 'Destino';

  @override
  String get small => 'Pequeño';

  @override
  String get medium => 'Mediano';

  @override
  String get large => 'Grande';

  @override
  String get enterVehicleVersion => 'Ingrese la versión del vehículo';

  @override
  String get enterVehicleCondition => 'Ingrese la condición del vehículo';

  @override
  String get pauseTrip => 'Pausar viaje';

  @override
  String get yesCancel => 'Sí, Cancelar';

  @override
  String get restTrip => 'Reiniciar viaje';

  @override
  String get areUSureCancel => '¿Está seguro de que desea cancelar este viaje?';

  @override
  String get areUSureCancelBooking =>
      '¿Está seguro de que desea cancelar esta reserva completa?';

  @override
  String get carCancelledSuccessfully => 'Auto cancelado con éxito';

  @override
  String get addedVehiclesInfo => 'Información de vehículos agregados';

  @override
  String get rejectedVehiclesInfo => 'Información de vehículos rechazados';

  @override
  String get pendingVehiclesInfo => 'Información de vehículos pendientes';

  @override
  String get allNotifications => 'Todas las notificaciones';

  @override
  String get noNotificationsFound => 'No se encontraron notificaciones';

  @override
  String get sooner => 'Pronto';

  @override
  String get language => 'Idioma';

  @override
  String get selectLanguage => 'Seleccionar idioma';

  @override
  String get locationServicesDisabled =>
      'Los servicios de ubicación están desactivados. Por favor actívalos para usar esta función.';

  @override
  String get locationPermissionsDenied =>
      'Los permisos de ubicación están denegados. Por favor actívalos para usar esta función.';

  @override
  String get locationPermissionsDeniedForever =>
      'Los permisos de ubicación están permanentemente denegados. Por favor actívalos en la configuración.';

  @override
  String get failedToGetLocation =>
      'No se pudo obtener la ubicación actual. Por favor, Inténtalo de nuevo.';

  @override
  String get searchAddress => 'Buscar dirección';

  @override
  String get search => 'Buscar';

  @override
  String get selectAnotherAddress =>
      'Algo salió mal, por favor selecciona otra dirección';

  @override
  String get somethingWentWrong =>
      'Algo salió mal, por favor inténtalo de nuevo';

  @override
  String get selectLocation => 'Seleccionar ubicación';

  @override
  String get selectLocationOnMap =>
      'Por favor selecciona una ubicación en el mapa';

  @override
  String get failedToGetLocationDetails =>
      'No se pudieron obtener los detalles de la ubicación';

  @override
  String get tripDetails => 'Detalles del viaje';

  @override
  String get dropAndPickupPerson => 'Persona de entrega y recogida';

  @override
  String get addDropAndPickupPerson =>
      '+ Agregar persona de entrega y recogida';

  @override
  String get day => 'día';

  @override
  String get clientName => 'Nombre del cliente';

  @override
  String get checklistType => 'Tipo de lista de verificación';

  @override
  String get pickup => 'Recogida';

  @override
  String get delivery => 'Entrega';

  @override
  String get fuelLevel => 'Nivel de combustible';

  @override
  String mileageAt(String type) {
    return 'Kilometraje en $type (En millas/hora)';
  }

  @override
  String dateLabel(String type) {
    return 'Fecha de $type';
  }

  @override
  String get carSizeLabel => 'Tamaño del auto:';

  @override
  String get carSizeSmall => 'Pequeño';

  @override
  String get carSizeMedium => 'Mediano';

  @override
  String get carSizeLarge => 'Grande';

  @override
  String get insuranceProvider => 'Proveedor de seguro';

  @override
  String get asWeHaveTwoDifferentPricingWeWillTakeTheLargestSumAmount =>
      'Como tenemos dos precios diferentes, Tomaremos el monto más alto como costo de remolque.';

  @override
  String get youHaveOptForWinchServiceSoYouCanNotDisableThisOption =>
      'Has optado por el servicio de cabrestante, por lo tanto no puedes desactivar esta opción.';

  @override
  String get type => 'Tipo';

  @override
  String get performedDuring => 'Realizado durante';

  @override
  String get report => 'Informe';

  @override
  String get viewLess => 'Ver menos';

  @override
  String get viewMore => 'Ver más';

  @override
  String get waitingList => 'Lista de espera';

  @override
  String get restedTrip => 'Viaje pausado';

  @override
  String get resetPassword => 'Restablecer contraseña';

  @override
  String get providerInfo => 'Información del proveedor';

  @override
  String get noTransporterFoundYet => 'Aún no se ha encontrado transportista';

  @override
  String get pleaseEnterValidPickupAddress =>
      'Por favor, Introduce una dirección de recogida válida';

  @override
  String get stopLocation => 'Ubicación de parada';

  @override
  String pleaseAssignCarWithin(String type) {
    return 'Este espacio se te ha asignado para $type. Si tienes autos restantes, Por favor asigna todos los autos a tu transportista preferido dentro de $type.';
  }

  @override
  String get noStockLocationFound =>
      'No se encontró ubicación de stock, por favor elige otra ubicación';

  @override
  String get pleaseSelectInsurance =>
      'Por favor, Selecciona un seguro para tu vehículo';

  @override
  String get thanksUForFeedback => 'Gracias por tus comentarios';

  @override
  String get noCheckListFound =>
      'Su vehículo aún no está listo. Una vez listo, Deberá aprobar una lista de verificación.';

  @override
  String get bookingNoteCreatedSuccess => 'Nota de reserva creada con éxito';

  @override
  String get bookingNoteDeletedSuccess => 'Nota de reserva eliminada con éxito';

  @override
  String get transporterDoesNotHave =>
      'El transportista no tiene opción de cabrestante.';

  @override
  String get transporterDoesNotMeet =>
      'El transportista no cumple con los requisitos del vehículo.';

  @override
  String get noAvailableSlot =>
      'No hay espacios disponibles para los vehículos restantes.';

  @override
  String get allVehicleAreAlready => 'Todos los vehículos ya están asignados.';

  @override
  String get enableToUploadImages =>
      'No se pudo cargar las imágenes, por favor inténtalo de nuevo más tarde.';

  @override
  String get pleaseAddBothPickupNDropInfo =>
      'Por favor, Agrega tanto la información de recogida como de entrega.';

  @override
  String get description => 'Descripción';

  @override
  String get pleaseDescribeIssue => 'Por favor, Describe tu problema';

  @override
  String get photosOptional => 'Fotos (opcional)';

  @override
  String get submit => 'Enviar';

  @override
  String get yourComplainSubmittedSuccessfully =>
      'Tu queja se ha enviado con éxito.';

  @override
  String get chooseAnAction => 'Elige una acción';

  @override
  String get camera => 'Cámara';

  @override
  String get gallery => 'Galería';

  @override
  String get maxImagesError => 'Se pueden seleccionar un máximo de 7 imágenes.';

  @override
  String get imagesNotUploaded => 'Las imágenes no se han cargado.';

  @override
  String get pleaseEnterDescription => 'Por favor, Ingresa tu descripción.';

  @override
  String get vehicleSize => 'Tamaño del vehículo';

  @override
  String get vehicleDescription => 'Descripción del vehículo';

  @override
  String get isWinchRequired => '¿Es necesario cabrestante?';

  @override
  String get pickupService => 'Servicio de recogida';

  @override
  String get minimumCost => 'Costo mínimo';

  @override
  String get pickupAddress => 'Dirección de recogida';

  @override
  String get charge => 'Costo';

  @override
  String get vehicleCharges => 'Cargos del vehículo';

  @override
  String get openMap => 'Abrir mapa';

  @override
  String get select => 'Seleccionar';

  @override
  String get winchSupport => 'Soporte de cabrestante';

  @override
  String get exception => 'Excepción';

  @override
  String get exceptionsSupport => 'Soporte de excepción';

  @override
  String get writeUrMessageHere => 'Write your message here';

  @override
  String get originStockAdmin => 'Administrador de stock de origen';

  @override
  String get dropStockAdmin => 'Administrador de stock de destino';

  @override
  String get readMore => 'leer más';

  @override
  String get readLess => 'leer menos';

  @override
  String get customerDetail => 'Detalles del cliente';

  @override
  String get addCustomerDetail => 'Agregar detalles del cliente';

  @override
  String get mobileNumber => 'Número de móvil';

  @override
  String get enterUrMobileNumber => 'Ingresa tu número de móvil';

  @override
  String get pleaseEnterUrMobileNumber =>
      'Por favor, Ingresa tu número de móvil';

  @override
  String get pleaseEnterValidMobileNumber =>
      'Por favor, Ingresa un número de móvil válido';

  @override
  String get chatInactive =>
      'El chat está inactivo. ¡Ya no puedes chatear aquí!';

  @override
  String get noTripDatFound => 'No se encontraron datos del viaje';

  @override
  String get noMessageYet => 'Aún no hay mensajes';

  @override
  String get noteForMobile =>
      'Nota: El número de móvil solo será visible para los transportistas y administradores de stock asociados con el viaje en el que selecciones el vehículo de recogida desde tu ubicación o la opción de cabrestante, o para transportistas asociados con una reserva exclusiva.';

  @override
  String get pleaseReview =>
      '* Por favor, Revisa cuidadosamente la lista de verificación. Una vez que hayas verificado todos los elementos, Continúa confirmando la lista. No podremos continuar hasta que se apruebe.';

  @override
  String get affectedTime => 'Tiempo afectado';

  @override
  String get confirmChecklist => 'Confirmar lista de verificación';

  @override
  String get hour => 'hora';

  @override
  String get addComments => 'Agregar comentarios';

  @override
  String get comments => 'Comentarios';

  @override
  String get pleaseWaitWeAreRedirectingStripe =>
      'Por favor espera, te estamos redirigiendo al portal de clientes de Stripe';

  @override
  String get pleaseWaitWeAreRedirectingPayment =>
      'Por favor espera, te estamos redirigiendo a la página de pago';

  @override
  String get customerDetailSavedSuccess =>
      'Detalles del cliente guardados con éxito';

  @override
  String get pleaseSearchOriginStopLocation =>
      'Primero busca la ubicación de origen antes de seleccionar la ubicación de stock';

  @override
  String get pleaseSearchDropStopLocation =>
      'Primero busca la ubicación de destino antes de seleccionar la ubicación de stock';

  @override
  String get anyUndeclared =>
      'Cualquier artículo adicional o modificación no declarada puede resultar en la cancelación del viaje';

  @override
  String get withoutRefund => 'sin reembolso.';

  @override
  String get itIsUserResponsibility =>
      'Es responsabilidad del usuario revelar todos los detalles relevantes antes de que comience el viaje.';

  @override
  String get pleaseAcceptDeclaration =>
      'Por favor, acepta la declaración anterior';

  @override
  String get removeAllVehicle => '- Eliminar todos los vehículos';

  @override
  String get chooseSlot => 'Elegir horario';

  @override
  String get car => 'Coche';

  @override
  String get totalCostSlot => 'Costo total por';

  @override
  String get slots => 'Horarios';

  @override
  String get bookingStatus => 'Estado de la reserva';

  @override
  String get status => 'Estado';

  @override
  String get myRating => 'Mi calificación';

  @override
  String get insuranceCharges =>
      'Los cargos del seguro varían según el tamaño de su vehículo';

  @override
  String get attachPhotos =>
      'Adjunte fotos de los compartimentos adicionales o modificaciones de su vehículo para que el equipo pueda revisar y validar que el vehículo se puede transportar sin problemas.';

  @override
  String get attachedPhotos => 'Fotos adjuntas';

  @override
  String get addAnyDiscrepancies =>
      'Agrega cualquier discrepancia u observación adicional sobre tu vehículo (por ejemplo, Artículos faltantes, Daños o cualquier cosa no listada arriba).';

  @override
  String get vehicleImages => 'Imágenes del vehículo';

  @override
  String get invalidRoute => 'Ruta no válida';

  @override
  String get bookingCancelledSuccessfully => 'Reserva cancelada con éxito';

  @override
  String get tripCancelledSuccessfully => 'Viaje cancelado con éxito';

  @override
  String get changeInTravelPlans => 'Cambio en los planes de viaje';

  @override
  String get bookingMadeAccidentally => 'Reserva realizada por error';

  @override
  String get foundBetterAlternative => 'Encontró una mejor alternativa';

  @override
  String get costTooHigh => 'El costo era demasiado alto';

  @override
  String get pickupIssue => 'Problemas con el lugar o el horario de recogida';

  @override
  String get needToEditBooking => 'Se necesitan cambios en la reserva';

  @override
  String get anyOtherReason => 'Cualquier otra razón';

  @override
  String get reason => 'Razón';

  @override
  String get pleaseSelectReason => 'Por favor, seleccione la razón';

  @override
  String get paymentSettlementSummary => 'Payment Settlement Summary';

  @override
  String get totalPaidAmount => 'Total Paid Amount';

  @override
  String get totalRefundAmount => 'Total Refund Amount';

  @override
  String get totalDueAmount => 'Total Due Amount';

  @override
  String get refundAmount => 'Refund Amount';

  @override
  String get dueAmount => 'Due Amount';

  @override
  String get refundCase => 'Refund Case';

  @override
  String get bookingCancelled => 'Booking Cancelled';

  @override
  String get serviceBreakdown => 'Service Breakdown';

  @override
  String get initialCharge => 'Initial Charge';

  @override
  String get deductedAmount => 'Deducted Amount';

  @override
  String get remainingToCollect => 'Remaining to Collect';

  @override
  String get refundableAmount => 'Refundable Amount';

  @override
  String get extraCharges => 'Extra Charges';

  @override
  String get dropOffStorage => 'Drop-off Storage';

  @override
  String get pickUpStorage => 'Pick-up Storage';

  @override
  String get proceedToSettlement => 'Proceed to Settlement';
}
