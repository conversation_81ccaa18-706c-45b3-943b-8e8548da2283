// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get helloWorld => 'hello world';

  @override
  String get byeWorld => 'bye world';

  @override
  String get just_now => 'Just now';

  @override
  String get min_ago => 'Minute ago';

  @override
  String get hour_ago => 'Hour ago';

  @override
  String get day_ago => 'Day ago';

  @override
  String get week_ago => 'Week ago';

  @override
  String get year_ago => 'Year ago';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get copied => 'Copied';

  @override
  String get pleaseEnterEmail => 'Please enter email';

  @override
  String get pleaseEnterValidEmail => 'Please enter valid email';

  @override
  String get pleaseEnterCurrentPassword => 'Please enter current password';

  @override
  String get pleaseEnterPassword => 'Please enter password';

  @override
  String get passwordMinLength => 'Password should be at least 8 characters.';

  @override
  String get passwordShouldBeAtLeast8Characters =>
      'Password should be at least 8 characters.';

  @override
  String get newPasswordAndOldPasswordCannotBeSame =>
      'New password and old password cannot be same';

  @override
  String get otpMinLength => 'Please enter valid OTP';

  @override
  String get pleaseEnterValidUsername => 'Please enter valid username';

  @override
  String get usernameMinLength => 'Please enter valid username';

  @override
  String get pleaseEnterName => 'Please enter name';

  @override
  String get nameMinLength => 'Please enter name';

  @override
  String get pleaseEnterFirstName => 'Please enter first name';

  @override
  String get firstNameMinLength => 'Please enter first name';

  @override
  String get pleaseEnterLastName => 'Please enter last name';

  @override
  String get lastNameMinLength => 'Please enter last name';

  @override
  String get passwordDoesNotMatch => 'Password does not match';

  @override
  String get accountRegisterSuccessfully =>
      'Account registered successfully, Please verify your email.';

  @override
  String get passwordIncludeLetterNumberSymbol =>
      'Password must include at least one uppercase letter, one lowercase letter, one number, and one special character.';

  @override
  String get checkYourEmail => 'Check your email';

  @override
  String get sentVerificationCode => 'We\'ve sent a verification code on ';

  @override
  String get verifyOtp => 'Verify OTP';

  @override
  String get pleaseEnterOtp => 'Please enter OTP!';

  @override
  String get pleaseEnterValidOtp => 'Please enter valid OTP!';

  @override
  String get didNtReceiveEmail => 'Didn\'t received the email? ';

  @override
  String get clickToResend => 'Click to resend';

  @override
  String get resetPasswordInstructions => 'We\'ll send you OTP in your email';

  @override
  String get sendAnEmail => 'Send an email';

  @override
  String get ultimateStressFreeCarTransport =>
      'The ultimate stress-free car\ntransport experience.';

  @override
  String get email => 'Email';

  @override
  String get enterYourEmail => 'Enter your email';

  @override
  String get password => 'Password';

  @override
  String get enterYourPassword => 'Enter your password';

  @override
  String get enterYourConfirmPassword => 'Enter your confirm password';

  @override
  String get forgotPassword => 'Forgot Password';

  @override
  String get forgotPasswordQuestion => 'Forgot Password?';

  @override
  String get logIn => 'Log In';

  @override
  String get doNtHaveAccount => 'Don\'t have an account?';

  @override
  String get signUp => 'Sign Up';

  @override
  String get setPassword => 'Set password';

  @override
  String get setNewPassword => 'Go ahead and set a new password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get pleaseEnterConfirmPassword => 'Please enter confirm password';

  @override
  String get pleaseEnterDetails =>
      'Please enter your details to create an account';

  @override
  String get name => 'Name';

  @override
  String get enterYourName => 'Enter your name';

  @override
  String get createAccount => 'Create an account';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get customer_support => 'Customer Support';

  @override
  String get payments => 'Payments';

  @override
  String get past_purchase => 'Past Purchase';

  @override
  String get sign_out => 'Sign Out';

  @override
  String get delete_account => 'Delete Account';

  @override
  String get edit_details => 'Edit Details';

  @override
  String get change_password => 'Change Password';

  @override
  String get save => 'Save';

  @override
  String get enter_your_name => 'Enter your name';

  @override
  String get enter_your_email => 'Enter your email';

  @override
  String get enter_your_password => 'Enter your password';

  @override
  String get set_new_password => 'Set new password';

  @override
  String get go_ahead_and_set_a_new_password =>
      'Go ahead and set a new password';

  @override
  String get old_password => 'Old Password';

  @override
  String get uploadCarImg => 'Upload car images';

  @override
  String get new_password => 'New Password';

  @override
  String get confirm_password => 'Confirm Password';

  @override
  String get please_enter_confirm_password => 'Please enter confirm password';

  @override
  String get transporter => 'Transporter';

  @override
  String get total_trip_cost => 'Total Trip Cost';

  @override
  String get no_of_vehicles => 'No. Of Vehicles';

  @override
  String get equipment_type => 'Equipment type';

  @override
  String get vehicles_info => 'Vehicles Info';

  @override
  String get details => 'Details';

  @override
  String get car_brand => 'Car brand';

  @override
  String get car_model => 'Car model';

  @override
  String get car_serial => 'Car serial #';

  @override
  String get car_year => 'Car year';

  @override
  String get view_details => 'View Details';

  @override
  String get stockLocations => 'Stock Locations';

  @override
  String get stockLocation => 'Stock Location';

  @override
  String get originStockLocation => 'Origin Stock Location';

  @override
  String get chooseOriginStockLocation => 'Choose origin stock location';

  @override
  String get pleaseSelectPickupDate => 'Please select pickup date';

  @override
  String get pleaseSelectDeliveryDate => 'Please select delivery date';

  @override
  String get pleaseSelectDelivery => 'Please select delivery date';

  @override
  String get dropStockLocation => 'Drop Stock Location';

  @override
  String get chooseDropStockLocation => 'Choose drop stock location';

  @override
  String get vehicleInfo => 'Vehicles Info';

  @override
  String get vehicleBrand => 'Vehicle brand';

  @override
  String get chooseVehicleBrand => 'Choose vehicle brand';

  @override
  String get vehicleModel => 'Vehicle model';

  @override
  String get chooseVehicleModel => 'Choose vehicle model';

  @override
  String get vehicleYear => 'Vehicle year';

  @override
  String get chooseVehicleYear => 'Choose vehicle year';

  @override
  String get vehicleCondition => 'Vehicle condition';

  @override
  String get chooseVehicleCondition => 'Write vehicle condition';

  @override
  String get pleaseDescribeTheIssue => 'Please describe the issue.';

  @override
  String get writeIssueDetailsHere => 'Write issue details here';

  @override
  String get vehicleSerialNo => 'Vehicle serial no.';

  @override
  String get vehicleSerialNumber => 'Vehicle serial number';

  @override
  String get iNeedMyCarToBePickedUpAndTakenToTheStockLocation =>
      'I need my car to be picked up and taken to the stock location.';

  @override
  String get enterPickupAddress => 'Enter pickup address';

  @override
  String get towingCost => 'Towing cost';

  @override
  String get drivingCostWithOperator => 'Driving cost with operator';

  @override
  String get addAnotherVehicle => 'Add Another Vehicle';

  @override
  String get pickupAndDeliveryDates => 'Pickup & Delivery Dates';

  @override
  String get pickupDate => 'Pickup date';

  @override
  String get deliveryDate => 'Delivery date';

  @override
  String get transportAllVehicleInOneTruck =>
      'Transport all vehicle in one truck';

  @override
  String get vehicleVersion => 'Vehicle Version';

  @override
  String get passChanged => 'Password changed successfully';

  @override
  String get findTransporter => 'Find Transporter';

  @override
  String get enterVBrandName => 'Enter brand of your vehicle';

  @override
  String enterBrandName(String enterBrandName) {
    return 'Enter brand of your $enterBrandName vehicle';
  }

  @override
  String get chooseVBrandName => 'Choose brand of your vehicle';

  @override
  String chooseBrandName(String brandName) {
    return 'Choose brand of your $brandName vehicle';
  }

  @override
  String get enterVYear => 'Enter year of your vehicle';

  @override
  String enterVehicleYear(String enterVehicleYear) {
    return 'Enter year of your $enterVehicleYear vehicle';
  }

  @override
  String get chooseVYear => 'Choose year of your vehicle';

  @override
  String pleaseChooseVehicleYear(String vehicleYear) {
    return 'Choose year of your $vehicleYear vehicle';
  }

  @override
  String get enterVModel => 'Enter model of your vehicle';

  @override
  String enterVehicleModel(String enterVehicleModel) {
    return 'Enter model of your $enterVehicleModel vehicle';
  }

  @override
  String get chooseVModel => 'Choose model of your vehicle';

  @override
  String pleaseChooseVehicleModel(String vehicleModel) {
    return 'Choose model of your $vehicleModel vehicle';
  }

  @override
  String get pleaseEnterVSerialNumber =>
      'Please enter serial number of your vehicle';

  @override
  String pleaseEnterSerialNumber(String serialNumber) {
    return 'Please enter serial number of your $serialNumber vehicle';
  }

  @override
  String get pleasePickUpAddress => 'Please enter your pickup address';

  @override
  String get bookingDataVerification =>
      'Booking data has been successfully submitted for car verification.';

  @override
  String get exclusiveCreated => 'Exclusive booking created successfully.';

  @override
  String get pleasePickUpZip => 'Please enter zip code of your pickup address';

  @override
  String get pleaseEnterDropUserName => 'Please enter drop user name';

  @override
  String get invalidDropUserName => 'Please enter valid drop user name';

  @override
  String get pleaseEnterDropUserDocType =>
      'Please enter drop user document type';

  @override
  String get pleaseEnterDropUserDoc => 'Please enter drop user document';

  @override
  String get pleaseEnterPickUserName => 'Please enter pick user name';

  @override
  String get invalidPickUserName => 'Please enter valid pick user name';

  @override
  String get pleaseEnterPickUserDocType =>
      'Please enter pick user document type';

  @override
  String get signOut => 'Sign Out';

  @override
  String get cancel => 'Cancel';

  @override
  String get newUpdateAvailable => 'New Update Available';

  @override
  String get updateMessage =>
      'We\'ve made improvements and fixed bugs to enhance your experience.Please update the app to continue using it smoothly.';

  @override
  String versionInfo(String currentAppVersion, String minimumAppVersion) {
    return '📱 Current version: $currentAppVersion\n✅ Required version: $minimumAppVersion';
  }

  @override
  String get updateNow => 'Update Now';

  @override
  String get troubleUpdating =>
      'Having trouble updating?\n Visit the app store manually or contact support';

  @override
  String get signOutContent => 'Are you sure you want to Sign Out?';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get deleteAccountContent =>
      'Are you sure you want to delete this account?';

  @override
  String get delete => 'Delete';

  @override
  String get searchOriginLocation => 'Search origin location';

  @override
  String get searchDropLocation => 'Search drop location';

  @override
  String get enterZipCode => 'Enter ZIP code';

  @override
  String get userPickUpLocation => 'User\'s Pick-Up location';

  @override
  String get userLocation => 'User\'s Location';

  @override
  String get userDeliveryLocation => 'User\'s Delivery location';

  @override
  String get toContinueWithTrip =>
      'To continue with exclusive trip you have to fill the Pick-Up and Delivery Location';

  @override
  String get otpSentSuccess => 'OTP sent successfully!';

  @override
  String get assigned => 'Assigned';

  @override
  String get spotAvailableReservation => 'Spot available for reservation';

  @override
  String get noOfVehicle => 'No. Of Vehicles';

  @override
  String get vehicleType => 'Vehicle Type';

  @override
  String get enterVehicleBrand => 'Enter vehicle brand';

  @override
  String get enterVehicleYearLabel => 'Enter vehicle year';

  @override
  String get enterVehicleModelLabel => 'Enter vehicle model';

  @override
  String get exclusiveTrip => 'Exclusive Trip';

  @override
  String get didNtFoundCar =>
      'Didn\'t found car? Send your car info to Verification';

  @override
  String get aPersonalized =>
      'A personalized service where your vehicle is picked up from your premises and delivered to your chosen location (if accessible). With no sharing and flexible scheduling, It offers exclusivity at a higher cost.';

  @override
  String get pleaseEnterPickUserDoc => 'Please enter pick user document';

  @override
  String get sendRequest => 'Send Request';

  @override
  String get sharedTrip => 'Shared Trip';

  @override
  String get transportUrVehicle =>
      'Transport your vehicle with others by dropping it at a designated pick-up point for delivery to a set location. This cost-effective option excludes home pick-up and delivery.';

  @override
  String get winchRequired => 'Winch required';

  @override
  String get noTransportersFound => 'No Any Transporters Available';

  @override
  String get noProvider => 'No provider';

  @override
  String get thereIsNoProviderWaitList =>
      'There is no provider with the requested characteristics, You can join waitlist or continue with exclusive trip';

  @override
  String get thereIsNoProvider =>
      'There is no provider with the requested characteristics, You can continue with exclusive trip';

  @override
  String get enterPersonName => 'Enter person\'s name';

  @override
  String get chooseIdType => 'Choose the ID proof type';

  @override
  String get idType => 'ID type';

  @override
  String get idProofPhoto => 'ID Proof photo';

  @override
  String get uploadPicture => 'Upload Pictures of your ID proof';

  @override
  String get tapThisAnd =>
      'Tap this and choose the pictures you want to upload from  your device';

  @override
  String get enterUrAddress => 'Enter your address';

  @override
  String get loading => 'Loading...';

  @override
  String get yourAddress => 'Your address not found';

  @override
  String get summary => 'Summary';

  @override
  String get transportationCost => 'Transportation Cost';

  @override
  String get dropOffStorageFee => 'Drop-off Storage Fee';

  @override
  String get pickupStorageFee => 'Pickup Storage Fee';

  @override
  String get serviceFee => 'Service Fee';

  @override
  String get dropTransportation => 'Drop-off Transportation Cost';

  @override
  String get includeInsurance => 'Include Insurance';

  @override
  String get noteEnsure =>
      'Note: Ensure your vehicle against any damage during transport.';

  @override
  String get total => 'Total';

  @override
  String noteUHaveToPay(String pay, String remain) {
    return 'Note: You have to pay $pay% amount of total amount right now at a time of booking and rest of the $remain% amount on the time of delivery.';
  }

  @override
  String get actualTransportationCost => 'Actual Transportation Cost';

  @override
  String get extraTransportationCost => 'Extra Transportation Cost';

  @override
  String get insuranceCost => 'Insurance Cost';

  @override
  String get totalInsuranceCost => 'Total Insurance Cost';

  @override
  String get exclusiveTripExtraCostNote =>
      'Due to it is exclusive trip you\'ve to pay extra transportation cost';

  @override
  String get grossAmount => 'Gross Amount';

  @override
  String get netAmount => 'Net Amount';

  @override
  String get startLocationStorageFee => 'Start Location Storage Fee';

  @override
  String get endLocationStorageFee => 'End Location Storage Fee';

  @override
  String get customerLocationToStartLocationServiceFee =>
      'Customer to Start Location Service Fee';

  @override
  String get netTransportationCost => 'Net Transportation Cost';

  @override
  String get netStartLocationStorageFee => 'Net Start Location Storage Fee';

  @override
  String get netEndLocationStorageFee => 'Net End Location Storage Fee';

  @override
  String get netCustomerLocationToStartLocationServiceFee =>
      'Net Customer to Start Location Service Fee';

  @override
  String get netInsuranceCost => 'Net Insurance Cost';

  @override
  String get netTripCharge => 'Net Trip Charge';

  @override
  String get totalTripCost => 'Total Trip Cost';

  @override
  String get netTotalAppFee => 'Net Total App Fee';

  @override
  String get totalNetTransportationCharge => 'Total Net Transportation Charge';

  @override
  String get totalNetInsuranceCharge => 'Total Net Insurance Charge';

  @override
  String get totalNetBookingPay => 'Total Net Booking Pay';

  @override
  String get transportationCostExplanation =>
      'Cost for transporting your vehicle from pickup to delivery location';

  @override
  String get storageCostExplanation =>
      'Storage fee for keeping your vehicle at our facility';

  @override
  String get insuranceCostExplanation =>
      'Optional insurance coverage to protect your vehicle during transport';

  @override
  String get platformFeeExplanation =>
      'Service fee for using our platform and booking management';

  @override
  String get dropOffServiceExplanation =>
      'Additional service for picking up your vehicle from your location';

  @override
  String get daysSelected => 'Days selected';

  @override
  String get perDayRate => 'Per day rate';

  @override
  String get totalDays => 'Total days';

  @override
  String get baseRate => 'Base rate';

  @override
  String get discountApplied => 'Discount applied';

  @override
  String get taxesAndFees => 'Taxes and fees';

  @override
  String get youPayNow => 'You pay now to confirm booking';

  @override
  String get remainingOnDelivery => 'Remaining amount due on delivery';

  @override
  String costBreakdownFor(String companyName) {
    return 'Cost breakdown for $companyName';
  }

  @override
  String get finalAmountToPay => 'Final amount to pay now';

  @override
  String get viewCarCharges => 'View Car Charges';

  @override
  String carChargesFor(String serialNumber) {
    return 'Car Charges for $serialNumber';
  }

  @override
  String get totalCost => 'Total Cost';

  @override
  String get taxAmount => 'Tax Amount';

  @override
  String get taxRate => 'Tax Rate';

  @override
  String get totalWithTax => 'Total with Tax';

  @override
  String get netTotalWithTax => 'Net Total with Tax';

  @override
  String get providerBreakdown => 'Provider Breakdown';

  @override
  String get overallSummary => 'Overall Summary';

  @override
  String get costBreakdown => 'Cost Breakdown';

  @override
  String get transportationCostDescription =>
      'Cost for transporting vehicles from pickup to delivery';

  @override
  String get startLocationStorage => 'Start Location Storage';

  @override
  String get startLocationStorageDescription =>
      'Storage fee at pickup location';

  @override
  String get endLocationStorage => 'End Location Storage';

  @override
  String get endLocationStorageDescription =>
      'Storage fee at delivery location';

  @override
  String get customerLocationService => 'Customer Location Service';

  @override
  String get customerLocationServiceDescription =>
      'Service for picking up from your location';

  @override
  String get insuranceCoverage => 'Insurance Coverage';

  @override
  String get insuranceCoverageDescription =>
      'Protection for your vehicles during transport';

  @override
  String vehicleWiseCharges(int count) {
    return 'Vehicle-wise Charges ($count vehicles)';
  }

  @override
  String get vehicle => 'Vehicle';

  @override
  String get transportation => 'Transportation';

  @override
  String get startStorage => 'Start Storage';

  @override
  String get endStorage => 'End Storage';

  @override
  String get customerService => 'Customer Service';

  @override
  String get insurance => 'Insurance';

  @override
  String get netYouPay => 'Net (You Pay)';

  @override
  String get finalAmountToPayTitle => 'Final Amount to Pay';

  @override
  String get payNowToConfirm => 'Pay now to confirm your booking';

  @override
  String get tax => 'Tax';

  @override
  String get totalAmountToPay => 'Total Amount to Pay';

  @override
  String get providerBreakdownTitle => 'Provider Breakdown';

  @override
  String get tripId => 'Trip ID';

  @override
  String get failedToFetchSuggestion => 'Failed to fetch suggestion';

  @override
  String get shipmentConfirmation => 'Shipment Confirmation';

  @override
  String get yourShipment => 'Your Shipment';

  @override
  String get noOfTotalVehicle => 'No. Of Total Vehicles';

  @override
  String get proceedToPayment => 'Proceed to Payments';

  @override
  String get pickupFrom => 'Pickup from';

  @override
  String get dropAt => 'Drop at';

  @override
  String get viewDetails => 'View Details';

  @override
  String get pickupLocation => 'Pickup Location';

  @override
  String get deliveryLocation => 'Delivery Location';

  @override
  String get notePleaseDrop =>
      'Note: Please drop off your vehicle one day before the scheduled date and pick it up one day after delivery to allow time for a condition checklist.';

  @override
  String get pleaseSelectDropDate =>
      'Please select drop date for your vehicle at stock location';

  @override
  String get pleaseSelectDropDateForAllVehicles =>
      'Please select drop date for your vehicle at stock location for all vehicles';

  @override
  String get carBrand => 'Car brand';

  @override
  String get carModel => 'Car model';

  @override
  String get carSerial => 'Car serial #';

  @override
  String get carYear => 'Car year';

  @override
  String get carSize => 'Car size';

  @override
  String get dropOffDate => 'Drop-off Date';

  @override
  String get selectVehicleDrop =>
      'Select vehicle drop off date at stock location';

  @override
  String get selectDateForPickupCar =>
      'Choose the date your vehicle is ready for pickup';

  @override
  String get storageFee => 'Storage Fee';

  @override
  String get perDay => 'per day';

  @override
  String get close => 'Close';

  @override
  String get continues => 'Continue';

  @override
  String get transportList => 'Transporters List';

  @override
  String get edit => 'Edit';

  @override
  String get chooseTransporter => 'Choose Transporter';

  @override
  String get filterBy => 'Filter By';

  @override
  String get noProviderFound => 'No any provider available';

  @override
  String get userInfo => 'User Info';

  @override
  String get carDropPerson => 'Car Drop person info';

  @override
  String get thisIsDropInfo =>
      'This is the info of the person who is going to drop the car';

  @override
  String get carPickupPerson => 'Car Pickup person info';

  @override
  String get thisIsDeliveryInfo =>
      'This is the info of the person who is going to take delivery';

  @override
  String get uAlreadyAssign =>
      'You already assign this vehicle, Please select another one';

  @override
  String get noSlotAvailable => 'No slot available for your next car';

  @override
  String get pleaseAssignCar => 'Please assign car for slot';

  @override
  String get pleaseChooseCar => 'Please choose a car for a slot';

  @override
  String get clearAll => 'Clear All';

  @override
  String get teamCapacity => 'Team Capacity';

  @override
  String get rating => 'Rating';

  @override
  String get rated => 'Rated';

  @override
  String get pricing => 'Pricing';

  @override
  String get lowestPerKM => 'Lowest Per K.M';

  @override
  String get in2Days => 'In 2 Days';

  @override
  String get rateProvider => 'Rate Provider';

  @override
  String get payRemainAmount => 'Pay Remain Amount';

  @override
  String get paySettlementAmount => 'Pay Settlement Amount';

  @override
  String get paymentSettlement => 'Payment Settlement';

  @override
  String get remainPayments => 'Remaining Payments';

  @override
  String get cancelTrip => 'Cancel Trip';

  @override
  String get inTrackTransport => 'InTrack Transport';

  @override
  String get addNotes => 'Add Notes';

  @override
  String get notes => 'Notes';

  @override
  String get pleaseAddNotes => 'Please add notes';

  @override
  String get reportsFromTransporter => 'Reports from transporter';

  @override
  String get writeNotes => 'Write Notes';

  @override
  String get writeUrMessage => 'Write your message here';

  @override
  String get upcoming => 'Upcoming';

  @override
  String get ongoing => 'Ongoing';

  @override
  String get completed => 'Completed';

  @override
  String get chatWithProvider => 'Chat with provider';

  @override
  String get checklist => 'Checklist';

  @override
  String get checklists => 'Checklists';

  @override
  String get mileageAtPickup => 'Mileage at Pickup';

  @override
  String get mileageAtDelivery => 'Mileage at Delivery';

  @override
  String get pickupDateNPlace => 'Pickup Date & Place';

  @override
  String get deliveryDateNPlace => 'Delivery Date & Place';

  @override
  String get pickupOfficer => 'Pickup Officer';

  @override
  String get deliveryOfficer => 'Delivery Officer';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get uploadPictureOfId => 'Upload Pictures of your ID proof';

  @override
  String get tapThisNChoose =>
      'Tap this and choose the pictures you want to upload from  your device';

  @override
  String get editDetails => 'Edit Details';

  @override
  String get originLocation => 'Origin Location';

  @override
  String get dropLocation => 'Drop Location';

  @override
  String get addMoreCars => 'Add more cars';

  @override
  String get paymentSummary => 'Payment Summary';

  @override
  String get paidAmount => 'Paid Amount';

  @override
  String get remainAmount => 'Remain Amount';

  @override
  String get remainAmountTax => 'Remain Amount Tax';

  @override
  String get cancelBooking => 'Cancel Booking';

  @override
  String get chooseWhatUFeel => 'Choose what you feel';

  @override
  String get writeSuggestion => 'Write Suggestion';

  @override
  String get writeUrSuggestion => 'Write your suggestion if you have';

  @override
  String get submitReview => 'Submit Review';

  @override
  String get viewCheckList => 'View Check List';

  @override
  String get removeCar => 'Remove Car';

  @override
  String get areUSureRemoveCar => 'Are you sure you want to remove this car?';

  @override
  String get noVehicleAvailable => 'No vehicle available';

  @override
  String get pleaseEnterPickLocation => 'Please enter your pickup location';

  @override
  String get pleaseEnterDeliveryLocation =>
      'Please enter your delivery location';

  @override
  String get submitForApproval => 'Submit Detail for Approval';

  @override
  String get assignedCars => 'Assigned Cars';

  @override
  String get howManyCar => 'How many cars you want to ship with this provider';

  @override
  String get chooseVehicle => 'Choose vehicle';

  @override
  String get assignMoreCar => 'Assign More Cars';

  @override
  String get user => 'User';

  @override
  String get userEmail => 'User Email';

  @override
  String get creditDebitCards => 'Credit & Debit Cards';

  @override
  String get bank => 'Bank';

  @override
  String get addAnotherMethod => 'Add Another Another Method';

  @override
  String get congratulation => 'Congratulations';

  @override
  String get youHaveGotTransportation =>
      'You have got the transportation spot. you can process further by making Payments';

  @override
  String get changeBid => 'Change Bid';

  @override
  String get yourCurrentBid => 'Your Current Bid';

  @override
  String get minimumBidRequired =>
      'Minimum bid required for your car to be transported';

  @override
  String get minimumRequirementFor =>
      'Minimum requirement for your car to be transported.';

  @override
  String get chooseYourBidIncrement => 'Choose your bid increment';

  @override
  String get yourNeWBidWillBe => 'Your New Bid will be';

  @override
  String get note => 'Note';

  @override
  String get requestedTrips => 'Requested Trips';

  @override
  String get acceptedTrips => 'Accepted Trips';

  @override
  String get yourPositionInList => 'Your position in List';

  @override
  String get enterAuction => 'Enter Auction';

  @override
  String get reject => 'Reject';

  @override
  String get dueToMoreUser =>
      'Due to more users than available spots, Priority for reserving spots will be given to those who offer a higher Payments for the spots';

  @override
  String get wait => 'Wait';

  @override
  String get joinWaitlist => 'Join Waitlist';

  @override
  String get areUSure => 'Are you sure?';

  @override
  String get uAreRejecting =>
      'You are rejecting this offer do you want to wait for another offer or want to go to waitlist';

  @override
  String get resumeTrip => 'Resume Trip';

  @override
  String get editTrip => 'Edit Trip';

  @override
  String get noTripFound => 'No any trip available';

  @override
  String get auction => 'Auction';

  @override
  String get closingIn => 'Closing In';

  @override
  String get opnTrackTransportation => 'OnTrack Transportation';

  @override
  String get availableSlot => 'Available Slots';

  @override
  String get noOfAvailableSlot => 'No.Of Available Slots';

  @override
  String get carsWanted => 'Cars wanted to Move';

  @override
  String get yourCurrentSpot => 'Your Current Spot';

  @override
  String get listOfCurrentBid => 'List of Current bids';

  @override
  String get theUsersListed =>
      'The users listed inside the box are the ones that will be transported.';

  @override
  String get exitAuction => 'Exit Auction';

  @override
  String get startBiding => 'Start Biding';

  @override
  String get uHaveNotBid => 'You haven\'t bid yet';

  @override
  String get accept => 'Accept';

  @override
  String get origin => 'Origin';

  @override
  String get destination => 'Destination';

  @override
  String get small => 'Small';

  @override
  String get medium => 'Medium';

  @override
  String get large => 'Large';

  @override
  String get enterVehicleVersion => 'Enter Vehicle Version';

  @override
  String get enterVehicleCondition => 'Enter Vehicle Condition';

  @override
  String get pauseTrip => 'Pause Trip';

  @override
  String get yesCancel => 'Yes, Cancel';

  @override
  String get restTrip => 'Rest Trip';

  @override
  String get areUSureCancel => 'Are you sure want to cancel this trip?';

  @override
  String get areUSureCancelBooking =>
      'Are you sure want to cancel this entire booking?';

  @override
  String get carCancelledSuccessfully => 'Car cancelled successfully';

  @override
  String get addedVehiclesInfo => 'Approved Vehicles Info';

  @override
  String get rejectedVehiclesInfo => 'Rejected Vehicles Info';

  @override
  String get pendingVehiclesInfo => 'Pending Vehicles Info';

  @override
  String get allNotifications => 'All Notifications';

  @override
  String get noNotificationsFound => 'No notification found';

  @override
  String get sooner => 'Sooner';

  @override
  String get language => 'Language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get locationServicesDisabled =>
      'Location services are disabled. Please enable them to use this feature.';

  @override
  String get locationPermissionsDenied =>
      'Location permissions are denied. Please enable them to use this feature.';

  @override
  String get locationPermissionsDeniedForever =>
      'Location permissions are permanently denied. Please enable them in settings.';

  @override
  String get failedToGetLocation =>
      'Failed to get current location. Please try again.';

  @override
  String get searchAddress => 'Search Address';

  @override
  String get search => 'Search';

  @override
  String get selectAnotherAddress =>
      'Something went wrong, Please select another address';

  @override
  String get somethingWentWrong => 'Something went wrong, Please try again';

  @override
  String get selectLocation => 'Select Location';

  @override
  String get selectLocationOnMap => 'Please select a location on the map';

  @override
  String get failedToGetLocationDetails => 'Failed to get location details';

  @override
  String get tripDetails => 'Trip Details';

  @override
  String get dropAndPickupPerson => 'Drop and Pickup person';

  @override
  String get addDropAndPickupPerson => '+ Add drop and pickup person';

  @override
  String get day => 'day';

  @override
  String get clientName => 'Client Name';

  @override
  String get checklistType => 'Checklist Type';

  @override
  String get pickup => 'Pickup';

  @override
  String get delivery => 'Delivery';

  @override
  String get fuelLevel => 'Fuel level';

  @override
  String mileageAt(String type) {
    return 'Mileage at $type (In miles/hr)';
  }

  @override
  String dateLabel(String type) {
    return '$type Date';
  }

  @override
  String get carSizeLabel => 'Car size:';

  @override
  String get carSizeSmall => 'Small';

  @override
  String get carSizeMedium => 'Medium';

  @override
  String get carSizeLarge => 'Large';

  @override
  String get insuranceProvider => 'Insurance provider';

  @override
  String get asWeHaveTwoDifferentPricingWeWillTakeTheLargestSumAmount =>
      'As we have two different pricing we will take the largest sum amount as towing cost.';

  @override
  String get youHaveOptForWinchServiceSoYouCanNotDisableThisOption =>
      'You have opt for winch service so you can not disable this option.';

  @override
  String get type => 'Type';

  @override
  String get performedDuring => 'Performed During';

  @override
  String get report => 'Report';

  @override
  String get viewLess => 'View less';

  @override
  String get viewMore => 'View more';

  @override
  String get waitingList => 'Waiting List';

  @override
  String get restedTrip => 'Rested Trip';

  @override
  String get resetPassword => 'Reset password';

  @override
  String get providerInfo => 'Provider Info';

  @override
  String get noTransporterFoundYet => 'No transporter found yet';

  @override
  String get pleaseEnterValidPickupAddress =>
      'Please enter valid pickup address';

  @override
  String get stopLocation => 'Stop Location';

  @override
  String pleaseAssignCarWithin(String type) {
    return 'This sloat assigned to you for $type. If you have any remaining cars, Please assign all cars to a your preferred transporter within $type.';
  }

  @override
  String get noStockLocationFound =>
      'No stock location found, Please choose another location';

  @override
  String get pleaseSelectInsurance =>
      'Please select insurance for your vehicle';

  @override
  String get thanksUForFeedback => 'Thank you for your feedback';

  @override
  String get noCheckListFound =>
      'Your vehicle isn’t performed yet. After it gets performed, You’ll have to approve a checklist.';

  @override
  String get bookingNoteCreatedSuccess => 'Booking note created successfully';

  @override
  String get bookingNoteDeletedSuccess => 'Booking note deleted successfully';

  @override
  String get transporterDoesNotHave =>
      'Transporter does not have winch option.';

  @override
  String get transporterDoesNotMeet =>
      'Transporter does not meet vehicle requirements.';

  @override
  String get noAvailableSlot => 'No available slots for remaining vehicles.';

  @override
  String get allVehicleAreAlready => 'All vehicles are already assigned.';

  @override
  String get enableToUploadImages =>
      'Enable to upload images, Please try again latter';

  @override
  String get pleaseAddBothPickupNDropInfo =>
      'Please add both pickup and drop info';

  @override
  String get description => 'Description';

  @override
  String get pleaseDescribeIssue => 'Please describe your issue';

  @override
  String get photosOptional => 'Photos (Optional)';

  @override
  String get submit => 'Submit';

  @override
  String get yourComplainSubmittedSuccessfully =>
      'Your Complain Submitted Successfully';

  @override
  String get chooseAnAction => 'Choose an Action';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get maxImagesError => 'Maximum 7 images can be selected.';

  @override
  String get imagesNotUploaded => 'Images are not uploaded';

  @override
  String get pleaseEnterDescription => 'Please enter your description';

  @override
  String get vehicleSize => 'Vehicle Size';

  @override
  String get vehicleDescription => 'Vehicle Description';

  @override
  String get isWinchRequired => 'Is winch required?';

  @override
  String get pickupService => 'Pickup Service';

  @override
  String get minimumCost => 'Minimum Cost';

  @override
  String get pickupAddress => 'Pickup Address';

  @override
  String get charge => 'Charge';

  @override
  String get vehicleCharges => 'Vehicle Charges';

  @override
  String get openMap => 'Open Map';

  @override
  String get select => 'Select';

  @override
  String get winchSupport => 'Winch Support';

  @override
  String get exception => 'Exception';

  @override
  String get exceptionsSupport => 'Exceptions Support';

  @override
  String get writeUrMessageHere => 'Write your message here';

  @override
  String get originStockAdmin => 'Origin stock admin';

  @override
  String get dropStockAdmin => 'Drop stock admin';

  @override
  String get readMore => 'read more';

  @override
  String get readLess => 'read less';

  @override
  String get customerDetail => 'Customer Detail';

  @override
  String get addCustomerDetail => 'Add Customer Detail';

  @override
  String get mobileNumber => 'Mobile Number';

  @override
  String get enterUrMobileNumber => 'Enter your mobile number';

  @override
  String get pleaseEnterUrMobileNumber => 'Please enter your mobile number';

  @override
  String get pleaseEnterValidMobileNumber => 'Please enter valid mobile number';

  @override
  String get chatInactive => 'Chat is inactive, You can not chat here anymore!';

  @override
  String get noTripDatFound => 'No any trip data available';

  @override
  String get noMessageYet => 'No messages yet';

  @override
  String get noteForMobile =>
      'Note: The mobile number is only visible to those transporters and stock administrators who are associated with the trip in which you select the pickup vehicle from your location or winch option, or transporters associated with exclusive booking.';

  @override
  String get pleaseReview =>
      '* Please review the checklist carefully. Once you\'ve verified all the items, Proceed by confirming the checklist. We won\'t be able to continue until the checklist is approved.';

  @override
  String get affectedTime => 'Affected time';

  @override
  String get confirmChecklist => 'Confirm Checklist';

  @override
  String get hour => 'hour';

  @override
  String get addComments => 'Add Comments';

  @override
  String get comments => 'Comments';

  @override
  String get pleaseWaitWeAreRedirectingStripe =>
      'Please wait we are redirecting you to Stripe customer portal';

  @override
  String get pleaseWaitWeAreRedirectingPayment =>
      'Please wait we are redirecting you to Payment page';

  @override
  String get customerDetailSavedSuccess => 'Customer detail saved successfully';

  @override
  String get pleaseSearchOriginStopLocation =>
      'Please first search origin stock location before you choose the stock location';

  @override
  String get pleaseSearchDropStopLocation =>
      'Please first search drop stock location before you choose the stock location';

  @override
  String get anyUndeclared =>
      'Any undeclared additional items or modifications may result in the cancellation of the trip ';

  @override
  String get withoutRefund => 'without refund.';

  @override
  String get itIsUserResponsibility =>
      ' It is the user`s responsibility to disclose all relevant details before the journey begins.';

  @override
  String get pleaseAcceptDeclaration => 'Please accept above declaration';

  @override
  String get removeAllVehicle => '- Remove all vehicle';

  @override
  String get chooseSlot => 'Choose Slot';

  @override
  String get car => 'Car';

  @override
  String get totalCostSlot => 'Total cost for';

  @override
  String get slots => 'Slots';

  @override
  String get bookingStatus => 'Booking status';

  @override
  String get status => 'Status';

  @override
  String get myRating => 'My Rating';

  @override
  String get insuranceCharges =>
      'Insurance charges varies upon your vehicle sizes';

  @override
  String get attachPhotos =>
      'Attach photos of your vehicle additional compartments or modifications allowing the team to review and validate that the vehicle can be transported without issues.';

  @override
  String get attachedPhotos => 'Attached photos';

  @override
  String get addAnyDiscrepancies =>
      'Add any discrepancies or additional observations about your vehicle (e.g., Missing items, Damage, or anything not listed above).';

  @override
  String get vehicleImages => 'Vehicle images';

  @override
  String get invalidRoute => 'Invalid route';

  @override
  String get bookingCancelledSuccessfully => 'Booking cancelled Successfully';

  @override
  String get tripCancelledSuccessfully => 'Trip cancelled Successfully';

  @override
  String get changeInTravelPlans => 'Change in travel plans';

  @override
  String get bookingMadeAccidentally => 'Booking made accidentally';

  @override
  String get foundBetterAlternative => 'Found a better alternative';

  @override
  String get costTooHigh => 'Cost was too high';

  @override
  String get pickupIssue => 'Issues with pickup location or timing';

  @override
  String get needToEditBooking => 'Changes needed in the booking';

  @override
  String get anyOtherReason => 'Any other reason';

  @override
  String get reason => 'Reason';

  @override
  String get pleaseSelectReason => 'Please select reason';

  @override
  String get paymentSettlementSummary => 'Payment Settlement Summary';

  @override
  String get totalPaidAmount => 'Total Paid Amount';

  @override
  String get totalRefundAmount => 'Total Refund Amount';

  @override
  String get totalDueAmount => 'Total Due Amount';

  @override
  String get refundAmount => 'Refund Amount';

  @override
  String get dueAmount => 'Due Amount';

  @override
  String get refundCase => 'Refund Case';

  @override
  String get bookingCancelled => 'Booking Cancelled';

  @override
  String get serviceBreakdown => 'Service Breakdown';

  @override
  String get initialCharge => 'Initial Charge';

  @override
  String get deductedAmount => 'Deducted Amount';

  @override
  String get remainingToCollect => 'Remaining to Collect';

  @override
  String get refundableAmount => 'Refundable Amount';

  @override
  String get extraCharges => 'Extra Charges';

  @override
  String get dropOffStorage => 'Drop-off Storage';

  @override
  String get pickUpStorage => 'Pick-up Storage';

  @override
  String get proceedToSettlement => 'Proceed to Settlement';
}
